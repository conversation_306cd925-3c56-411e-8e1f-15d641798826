<?php
$totalCreditUnit = $courses->sum('credit');
?>

<div class="p-6">
    <div class="overflow-x-auto">
        <table class="table-auto text-sm border border-gray-300 w-full">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Matric no.</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Semester summary</th>
                </tr>
                <tr class="bg-gray-50">
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="border px-2 py-1 text-center">Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Carry-over courses">Outstanding</th>
                </tr>
            </thead>
            <tbody>
                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $filteredRecords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $record): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php
                $registration = $record->registrations->first();

                if (!$registration) {
                $gpa = 0;
                $remark = null;
                $outstandingCourses = 'NIL';
                } else {
                $totalGradePoint = 0;
                $outstanding = [];

                foreach ($registration->totalScores as $score) {
                $grade = $grades->first(fn($g) => $score->total >= $g->min_score && $score->total <= $g->max_score);

                    if ($grade && $score->course) {
                    $totalGradePoint += $grade->point * $score->course->credit;
                    }

                    if ($score->total <= $failedScore && $score->course) {
                        $outstanding[] = $score->course->code;
                        }
                        }

                        $gpa = $totalCreditUnit > 0 ? number_format($totalGradePoint / $totalCreditUnit, 2) : 0;
                        $remark = $grades->first(fn($g) => $gpa >= $g->min_point && $gpa <= $g->max_point);
                            $outstandingCourses = $outstanding ? implode(', ', $outstanding) : 'NIL';
                            }
                            ?>
                            <tr>
                                <td class="border px-2 py-1 text-center"><?php echo e($index + 1); ?></td>
                                <td class="border px-2 py-1"><?php echo e($record->name); ?></td>
                                <td class="border px-2 py-1"><?php echo e($record->matric_number); ?></td>
                                <td class="border px-2 py-1"><?php echo e($gpa); ?></td>
                                <td class="border px-2 py-1"><?php echo e($remark?->remark ?? '-'); ?></td>
                                <td class="border px-2 py-1">
                                    <div class="max-w-[150px] overflow-auto max-h-[50px]">
                                        <?php echo e($outstandingCourses); ?>

                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="border px-2 py-1">No students found.</td>
                            </tr>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </tbody>
        </table>
    </div>
</div><?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/pages/overview.blade.php ENDPATH**/ ?>
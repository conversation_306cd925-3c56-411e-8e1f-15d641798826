<?php
use App\Filament\Staff\Resources\OverviewResource;

// Get courses for the current semester
$courses = OverviewResource::getCourses($this);
?>

<div class="p-6">
    <div class="overflow-x-auto">
        <table class="table-auto text-sm border border-gray-300 w-full">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Matric no.</th>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <th class="border px-2 py-1 text-center" colspan="4" x-tooltip.raw="<?php echo e($course->title); ?>">
                        <?php echo e($course->code); ?>

                        <span class="text-gray-400"><?php echo e($course->credit); ?><?php echo e($course->course_status->value); ?></span>
                    </th>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <th class="border px-2 py-1 text-center" colspan="3">Semester summary</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Cumulative summary</th>
                </tr>
                <tr class="bg-gray-50">
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Score">S</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade">G</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Point">P</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="border px-2 py-1 text-center">Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Carry-over courses">Outstanding</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Remark">C. Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Outstanding Courses">C.
                        Outstanding</th>
                </tr>
            </thead>
            <tbody>
                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $filteredRecords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $record): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <?php
                // Semester calculations using OverviewResource methods
                $semesterCourses = OverviewResource::getCourses($this);
                $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($semesterCourses);
                $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePoint($this, $record,
                $semesterCourses);
                $gpa = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit,
                2) : 0;
                $remark = OverviewResource::getRemarkFromGradePointAverage($gpa);
                $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCourses($this, $record,
                $semesterCourses);
                $outstandingCourses = $semesterOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';

                // Cumulative calculations using OverviewResource methods
                $cumulativeTotalCreditUnit = OverviewResource::getCumulativeTotalCreditUnit($this, $record);
                $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePoint($this, $record);
                $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
                $cumulativeTotalCreditUnit, 2) : 0;
                $cumulativeRemark = OverviewResource::getRemarkFromGradePointAverage($cgpa);
                $cumulativeOutstandingCourses = OverviewResource::getCumulativeOutstandingCourses($record, $this);
                $cumulativeOutstanding = $cumulativeOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';
                ?>
                <tr>
                    <td class="border px-2 py-1 "><?php echo e($index + 1); ?></td>
                    <td class="border px-2 py-1"><?php echo e($record->name); ?></td>
                    <td class="border px-2 py-1"><?php echo e($record->matric_number); ?></td>

                    <td class="border px-2 py-1 "><?php echo e($gpa); ?></td>
                    <td class="border px-2 py-1 "><?php echo e($remark?->remark ?? '-'); ?></td>
                    <td class="border px-2 py-1">
                        <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">
                            <?php echo e($outstandingCourses); ?>

                        </div>
                    </td>
                    <td class="border px-2 py-1 "><?php echo e($cgpa); ?></td>
                    <td class="border px-2 py-1 "><?php echo e($cumulativeRemark?->remark ?? '-'); ?></td>
                    <td class="border px-2 py-1">
                        <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">
                            <?php echo e($cumulativeOutstanding); ?>

                        </div>
                    </td>

                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                    $courseData = OverviewResource::getCourseData($this, $record, $course);
                    $failedScore = OverviewResource::getFailedScore();
                    ?>
                    <td
                        class="border px-2 py-1 text-center <?php echo e(($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : ''); ?>">
                        <?php echo e($courseData['total_score'] ?? '-'); ?>

                    </td>
                    <td
                        class="border px-2 py-1 text-center <?php echo e(($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : ''); ?>">
                        <?php echo e($courseData['grade'] ?? '-'); ?>

                    </td>
                    <td class="border px-2 py-1 text-center">
                        <?php echo e($courseData['point'] ?? '-'); ?>

                    </td>
                    <td class="border px-2 py-1 text-center">
                        <?php echo e($courseData['grade_point'] ?? '-'); ?>

                    </td>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="<?php echo e(9 + ($courses->count() * 4)); ?>" class="border px-2 py-1 text-center">No students
                        found.</td>
                </tr>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </tbody>
        </table>
    </div>
</div><?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/pages/overview.blade.php ENDPATH**/ ?>
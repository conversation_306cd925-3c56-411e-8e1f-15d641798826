<div class="p-6">
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $tableFilters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php echo e($key); ?>: <?php echo e($value); ?>

    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $filteredRecords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $record): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <h4><?php echo e($record->name); ?></h4>

    <!--[if BLOCK]><![endif]--><?php if($record->registrations->isNotEmpty()): ?>
    <ul>
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $record->registrations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $registration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <li>
            <?php echo e($registration->school_session_id); ?> -
            <?php echo e($registration->semester_id); ?> -
            <?php echo e($registration->level_id); ?>

        </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </ul>
    <?php else: ?>
    <p>No registrations found.</p>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

</div><?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/pages/overview.blade.php ENDPATH**/ ?>
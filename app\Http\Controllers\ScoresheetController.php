<?php

namespace App\Http\Controllers;

use App\Models\Level;
use App\Models\Course;
use App\Models\Grade;
use App\Models\Semester;
use App\Models\Department;
use Illuminate\Support\Str;
use App\Models\SchoolSession;
use App\Settings\CollegeSettings;
use <PERSON><PERSON>\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Cache;

class ScoresheetController extends Controller
{

    public function print($scoresheetCacheKey)
    {
        return view('filament.documents.scoresheet', $this->getScoreData($scoresheetCacheKey));
    }

    public function download($scoresheetCacheKey)
    {
        $scoreData = $this->getScoreData($scoresheetCacheKey);

        return Pdf::view('filament.documents.scoresheet', $scoreData)
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name($scoreData['fileName'] . '.pdf')
            ->download();
    }

    private function getScoreData($scoresheetCacheKey)
    {
        $scoreData = Cache::get($scoresheetCacheKey);

        if (! $scoreData) {

            abort(419, 'Page expired. Please regenerate the scoresheet.');
        }

        $session = isset($scoreData['tableFilters']['school_session_id'])
            ? SchoolSession::find($scoreData['tableFilters']['school_session_id'])?->name
            : null;

        $semester = isset($scoreData['tableFilters']['semester_id'])
            ? Semester::find($scoreData['tableFilters']['semester_id'])?->name
            : null;

        $level = isset($scoreData['tableFilters']['level_id'])
            ? Level::find($scoreData['tableFilters']['level_id'])?->name
            : null;

        $department = isset($scoreData['tableFilters']['department_id'])
            ? Department::find($scoreData['tableFilters']['department_id'])?->name
            : null;

        $course = isset($scoreData['tableFilters']['course_id'])
            ? Course::find($scoreData['tableFilters']['course_id'])
            : null;

        $fileName = 'Scoresheet - ' . Str::slug(Str::replace('/', '-', implode(' ', [
            $session,
            $semester,
            $level,
            $department,
            $course?->code,
        ])));

        $collegeSettings = app(CollegeSettings::class);
        $grades = Grade::orderBy('min_score', 'desc')->get();

        return (array) $scoreData + [
            'session'         => $session,
            'semester'        => $semester,
            'level'           => $level,
            'department'      => $department,
            'course'          => $course,
            'fileName'        => $fileName,
            'collegeSettings' => $collegeSettings,
            'grades'          => $grades,
        ];
    }
}

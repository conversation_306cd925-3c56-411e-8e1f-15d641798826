<?php

namespace App\Filament\Staff\Resources;

use App\Enums\AdmissionStatus;
use App\Enums\Role;
use App\Filament\Staff\Resources\OverviewResource\Pages;
use App\Models\Course;
use App\Models\Department;
use App\Models\Grade;
use App\Models\Level;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Scoresheet;
use App\Models\Semester;
use App\Models\TotalScore;
use App\Models\User;
use Filament\Forms\Components\Select;
use Filament\Resources\Resource;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\HtmlString;

class OverviewResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?int $navigationSort = 3;

    protected static ?string $modelLabel = 'Overview';

    protected static ?string $pluralModelLabel = 'Overview';

    protected static ?string $navigationGroup = 'Result';

    public static function canAccess(): bool
    {
        return management_staff_access();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('role', Role::STUDENT)
            ->whereHas('application', fn ($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->with(['registrations']);
    }

    public static function table(Table $table): Table
    {

        return $table
            ->deferLoading()
            ->deferFilters()
            // ->persistFiltersInSession()
            ->paginated([10, 20, 30, 40, 50])
            ->striped()
            ->recordAction(null)
            ->defaultSort('last_name')
            ->emptyStateHeading(fn (HasTable $livewire) => new HtmlString(self::getEmptyStateHeading($livewire)))
            ->emptyStateDescription(fn (HasTable $livewire) => new HtmlString(self::getEmptyStateDescription($livewire)))
            ->description('Overview provides a comprehensive summary of students’ academic performance.')
            ->searchPlaceholder('Search (student name)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->sortable(
                        query: fn ($query, $direction) => $query->orderBy('users.last_name', $direction)
                    )
                    ->searchable(['last_name', 'first_name', 'middle_name']),
                TextColumn::make('matric_number')
                    ->label('Matric no.'),
                ColumnGroup::make('Semester summary')
                    ->columns([
                        TextColumn::make('gpa')
                            ->label(new HtmlString("<div x-tooltip=\"'Grade Point Average'\">GPA</div>"))
                            ->state(function ($record, $livewire) {
                                $courses = self::getCourses($livewire);
                                $record->semesterCourses = $courses;
                                $totalCreditUnit = self::getSemesterTotalCreditUnit($courses) ?? 0;
                                $gradePointAverage = $totalCreditUnit > 0 ? number_format(self::getSemesterTotalGradePoint($livewire, $record, $courses) / $totalCreditUnit, 2) : 0;
                                $record->calculatedGradePointAverage = $gradePointAverage;

                                return $gradePointAverage;
                            }),
                        TextColumn::make('remark')
                            ->state(fn ($record) => self::getRemarkFromGradePointAverage($record->calculatedGradePointAverage)?->remark),
                        TextColumn::make('outstanding')
                            ->words(4)
                            ->tooltip(function ($state): ?string {
                                $courseCount = count(array_filter(explode(',', $state)));

                                if ($courseCount <= 2) {
                                    return null;
                                }

                                return $state;
                            })
                            ->label(new HtmlString("<div x-tooltip=\"'Carry-over courses'\">Outstanding</div>"))
                            ->state(function ($record, $livewire) {
                                $courses = $record->semesterCourses ?? self::getCourses($livewire);

                                return self::getSemesterOutstandingCourses($livewire, $record, $courses)->pluck('code')->implode(', ');
                            }),
                    ])->alignment(Alignment::Center),
                // ...self::getCourseColumns($livewire),
            ])

            ->filters([
                SelectFilter::make('overview_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->required()
                            ->label('Session')
                            ->native(false)
                            ->options(function () {
                                return SchoolSession::query()
                                    ->orderBy('name', 'desc')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSchoolSession()->id),
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->native(false)
                            ->options(function () {
                                return Semester::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSemester()?->id),
                        Select::make('level_id')
                            ->required()
                            ->label('Level')
                            ->native(false)
                            ->options(function () {
                                return Level::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                        Select::make('department_id')
                            ->required()
                            ->label('Department')
                            ->native(false)
                            ->searchable()
                            ->options(function () {
                                return Department::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, HasTable $livewire): Builder {
                        if (! self::hasRequiredFilters($livewire) || ! self::isScoresheetAvailable($livewire) || ! self::isCourseAvailable($livewire)) {
                            return $query->whereRaw('1 = 0');
                        }

                        return $query;
                    })
                    ->query(function (Builder $query, array $data) {
                        $department = Department::find($data['department_id']);

                        return $query->whereHas('registrations', function ($q) use ($data, $department) {
                            $q->where('school_session_id', $data['school_session_id'])
                                ->where('semester_id', $data['semester_id'])
                                ->where('level_id', $data['level_id']);

                            if (! ($department?->is_edu || $department?->is_gse)) {
                                $q->where(function ($q) use ($data) {
                                    $q->whereHas('programme', function ($q) use ($data) {
                                        $q->where('first_department_id', $data['department_id'])
                                            ->orWhere('second_department_id', $data['department_id']);
                                    });
                                });
                            }
                        });
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            if ($name = SchoolSession::find($sessionId)?->name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            if ($name = Semester::find($semesterId)?->name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        if ($levelId = $data['level_id'] ?? null) {
                            if ($name = Level::find($levelId)?->name) {
                                $indicators[] = Indicator::make("Level: {$name}")->removable(false);
                            }
                        }

                        if ($deptId = $data['department_id'] ?? null) {
                            if ($name = Department::find($deptId)?->name) {
                                $indicators[] = Indicator::make("Department: {$name}")->removable(false);
                            }
                        }

                        return $indicators;
                    }),

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn (Action $action) => $action->label('View overview'),
            );
    }

    public static function getCourses(HasTable $livewire)
    {
        $filters = self::extractFilters($livewire);

        return Course::select('id', 'code', 'title', 'credit', 'course_status')
            ->where('department_id', $filters['department_id'])
            ->where('level_id', $filters['level_id'])
            ->where('semester_id', $filters['semester_id'])
            ->get();
    }

    public static function getGradeFromScore(?int $totalScore, ?Collection $grades = null)
    {
        if ($totalScore === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();

        return $grades->first(
            fn ($grade) => $totalScore >= $grade->min_score && $totalScore <= $grade->max_score
        );
    }

    public static function getRemarkFromGradePointAverage(?float $gradePointAverage, ?Collection $grades = null)
    {
        if ($gradePointAverage === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();

        return $grades->first(
            fn ($grade) => $gradePointAverage >= $grade->min_point && $gradePointAverage <= $grade->max_point
        );
    }

    private static function getRegistration(int $userId, array $filters): ?Registration
    {
        return Registration::where([
            'user_id' => $userId,
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'level_id' => $filters['level_id'],
        ])->first();
    }

    public static function getFailedScore(): int
    {
        static $failedScore;
        $failedScore ??= Grade::where('min_score', 0)->value('max_score');

        return $failedScore;
    }

    // private static function getCourseColumns(HasTable $livewire): array
    // {

    //     $courses = self::getCourses($livewire);

    //     return $courses
    //         ->map(fn ($course) => self::createCourseColumn($course))
    //         ->toArray();
    // }

    // private static function createCourseColumn($course): ColumnGroup
    // {
    //     $failedScore = self::getFailedScore();

    //     return ColumnGroup::make("title.{$course->id}")
    //         ->label(new HtmlString("<div x-tooltip=\"'{$course->title}'\">{$course->code} <span class='text-gray-400'>{$course->credit}{$course->course_status->value}</span></div>"))
    //         ->columns([
    //             TextColumn::make("score.{$course->id}")
    //                 ->label(new HtmlString("<div x-tooltip=\"'Score'\"> S </div>"))
    //                 ->color(fn ($state): string => ($state <= $failedScore) ? 'danger' : 'black')
    //                 ->state(function ($record, $livewire) use ($course) {
    //                     $courseData = self::getCourseData($livewire, $record, $course);
    //                     $record->courseData = $courseData;

    //                     return $courseData['score'] ?? '-';
    //                 }),
    //             TextColumn::make("grade.{$course->id}")
    //                 ->label(new HtmlString("<div x-tooltip=\"'Grade'\">G</div>"))
    //                 ->color(fn ($record): string => ($record->courseData['score'] <= $failedScore) ? 'danger' : 'black')
    //                 ->state(fn ($record) => $record->courseData['grade'] ?? '-'),
    //             TextColumn::make("point.{$course->id}")
    //                 ->label(new HtmlString("<div x-tooltip=\"'Point'\">P</div>"))
    //                 ->state(fn ($record) => $record->courseData['point'] ?? '-'),
    //             TextColumn::make("grade_point.{$course->id}")
    //                 ->label(new HtmlString("<div x-tooltip=\"'Grade Point'\">GP</div>"))
    //                 ->state(fn ($record) => $record->courseData['grade_point'] ?? '-'),
    //         ])
    //         ->alignment(Alignment::Center);
    // }

    public static function getCourseData(HasTable $livewire, $record, $course)
    {
        $filters = self::extractFilters($livewire);
        $registration = self::getRegistration($record->id, $filters);

        if (! $registration) {
            return null;
        }

        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore);

        return [
            'total_score' => $totalScore,
            'grade' => $grade?->name,
            'point' => $grade?->point,
            'grade_point' => $grade ? $grade->point * $course->credit : null,
        ];
    }

    public static function getSemesterTotalCreditUnit($courses)
    {
        return $courses->sum('credit');
    }

    public static function getSemesterGradePoint(Registration $registration, Course $course, Collection $grades): ?float
    {
        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore, $grades);

        return $grade ? (float) ($grade->point * $course->credit) : null;
    }

    public static function getSemesterTotalGradePoint(HasTable $livewire, $record, $courses)
    {
        return $courses->map(fn ($course) => self::getCourseData($livewire, $record, $course)['grade_point'])->sum();
    }

    public static function getSemesterOutstandingCourses(HasTable $livewire, $record, $courses): Collection
    {

        return $courses->filter(function ($course) use ($livewire, $record) {
            $data = self::getCourseData($livewire, $record, $course);

            return $data && ($data['total_score'] ?? -1) <= self::getFailedScore();
        });
    }

    public static function getCumulativeTotalCreditUnit(HasTable $livewire, User $student): int
    {
        $filters = self::extractFilters($livewire);
        $registrations = $student->registrations;

        // Collect all registered combinations of level, semester, and session
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        // Filter only those combinations where result is published
        $publishedCombos = $registeredCombos->filter(function ($combo) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $combo['semester_id'],
                'school_session_id' => $combo['school_session_id'],
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        })->values();

        if ($publishedCombos->isEmpty()) {
            return 0;
        }

        // Fetch courses only for those published level + semester combos
        $courses = Course::select('credit')
            ->where('department_id', $filters['department_id'])
            ->where(function ($query) use ($publishedCombos) {
                foreach ($publishedCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        return $courses->sum('credit');
    }

    public static function getCumulativeTotalGradePoint(HasTable $livewire, User $student): float
    {
        $filters = self::extractFilters($livewire);
        $registrations = $student->registrations;
        $totalGradePoint = 0.0;

        // Collect all registered combinations of level, semester, and session
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        // Filter only those combinations where result is published
        $publishedCombos = $registeredCombos->filter(function ($combo) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $combo['semester_id'],
                'school_session_id' => $combo['school_session_id'],
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        })->values();

        if ($publishedCombos->isEmpty()) {
            return 0.0;
        }

        // Fetch courses only for those published level + semester combos
        $courses = Course::select('id', 'credit', 'level_id', 'semester_id')
            ->where('department_id', $filters['department_id'])
            ->where(function ($query) use ($publishedCombos) {
                foreach ($publishedCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        // Calculate grade points for each course
        foreach ($courses as $course) {
            $combo = $publishedCombos->first(function ($item) use ($course) {
                return $item['level_id'] == $course->level_id && $item['semester_id'] == $course->semester_id;
            });

            if (! $combo) {
                continue;
            }

            $registration = $student->registrations->first(function ($reg) use ($combo) {
                return $reg->level_id == $combo['level_id'] &&
                    $reg->semester_id == $combo['semester_id'] &&
                    $reg->school_session_id == $combo['school_session_id'];
            });

            if (! $registration) {
                continue;
            }

            $totalScore = TotalScore::where([
                'registration_id' => $registration->id,
                'course_id' => $course->id,
            ])->value('total');

            if ($totalScore !== null) {
                $grade = self::getGradeFromScore($totalScore);
                if ($grade) {
                    $totalGradePoint += $grade->point * $course->credit;
                }
            }
        }

        return $totalGradePoint;
    }

    public static function getCumulativeOutstandingCourses(User $record, HasTable $livewire)
    {
        $failedScore = self::getFailedScore();
        $filters = self::extractFilters($livewire);
        $registrations = $record->registrations;

        // Collect all registered combinations of level, semester, and session
        $registeredCombos = $registrations->map(function ($reg) {
            return [
                'level_id' => $reg->level_id,
                'semester_id' => $reg->semester_id,
                'school_session_id' => $reg->school_session_id,
            ];
        });

        // Filter only those combinations where result is published
        $publishedCombos = $registeredCombos->filter(function ($combo) use ($filters) {
            return Scoresheet::where([
                'semester_id' => $combo['semester_id'],
                'school_session_id' => $combo['school_session_id'],
                'department_id' => $filters['department_id'],
                'is_published' => true,
            ])->exists();
        })->values();

        if ($publishedCombos->isEmpty()) {
            return collect();
        }

        // Fetch courses only for those published level + semester combos
        $courses = Course::select('id', 'code', 'level_id', 'semester_id')
            ->where('department_id', $filters['department_id'])
            ->where(function ($query) use ($publishedCombos) {
                foreach ($publishedCombos as $combo) {
                    $query->orWhere(function ($sub) use ($combo) {
                        $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                    });
                }
            })
            ->get();

        // Filter failed courses based on registration and published combo
        $failedCourses = $courses->filter(function ($course) use ($record, $failedScore, $publishedCombos) {
            $combo = $publishedCombos->first(function ($item) use ($course) {
                return $item['level_id'] == $course->level_id && $item['semester_id'] == $course->semester_id;
            });

            if (! $combo) {
                return false;
            }

            $registration = $record->registrations->first(function ($reg) use ($combo) {
                return $reg->level_id == $combo['level_id'] &&
                    $reg->semester_id == $combo['semester_id'] &&
                    $reg->school_session_id == $combo['school_session_id'];
            });

            if (! $registration) {
                return false;
            }

            $totalScore = TotalScore::where([
                'registration_id' => $registration->id,
                'course_id' => $course->id,
            ])->value('total');

            return $totalScore <= $failedScore;
        });

        return $failedCourses;
    }

    private static function isScoresheetAvailable(HasTable $livewire): bool
    {
        return self::isScoresheetCreated($livewire) && self::isScoresheetPublished($livewire);
    }

    private static function isCourseAvailable(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Course::where([
            'level_id' => $filters['level_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
        ])->exists();
    }

    public static function isScoresheetCreated(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Scoresheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
        ])->exists();
    }

    public static function isScoresheetPublished(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return Scoresheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
            'is_published' => true,
        ])->exists();
    }

    private static function getEmptyStateHeading(HasTable $livewire): string
    {
        if (! self::hasRequiredFilters($livewire)) {
            return 'All Options Must Be Selected to View Overview';
        }

        if (! self::isScoresheetCreated($livewire)) {
            return 'Scoresheet Has Not Been Created Yet';
        }

        if (! self::isScoresheetPublished($livewire)) {
            return 'Scoresheet Has Not Been Published Yet';
        }

        if (! self::isCourseAvailable($livewire)) {
            return 'No Courses Found';
        }

        return 'No Overview Found';
    }

    private static function getEmptyStateDescription(HasTable $livewire): string
    {
        if (! self::hasRequiredFilters($livewire)) {
            return 'Select <b>session</b>, <b>semester</b>, <b>level</b>, and <b>department</b> to view the overview.';
        }

        if (! self::isScoresheetCreated($livewire)) {
            return 'The scoresheet for this session, semester, and department has not been created yet. <b>Contact your school admin</b> for more information.';
        }

        if (! self::isScoresheetPublished($livewire)) {
            return 'The scoresheet for this session, semester, and department has not been published yet. <b>Contact your school admin</b> for more information.';
        }

        if (! self::isCourseAvailable($livewire)) {
            return 'No courses found for this session, semester, level, and department.';
        }

        return 'No overview found for the selected options.';
    }

    private static function extractFilters(HasTable $livewire): array
    {

        $filters = $livewire->tableFilters['overview_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
        ];
    }

    private static function hasRequiredFilters(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return ! empty($filters['school_session_id'])
            && ! empty($filters['semester_id'])
            && ! empty($filters['level_id'])
            && ! empty($filters['department_id']);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOverview::route('/'),
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Level;
use App\Models\Semester;
use App\Models\Department;
use Illuminate\Support\Str;
use App\Models\SchoolSession;
use App\Settings\CollegeSettings;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Cache;

class ResultController extends Controller
{

    public function print($resultCacheKey)
    {
        return view('filament.documents.result', $this->getResultData($resultCacheKey));
    }

    public function download($resultCacheKey)
    {
        $resultData = $this->getResultData($resultCacheKey);

        return Pdf::view('filament.documents.result', $resultData)
            ->withBrowsershot(fn(Browsershot $browsershot) => $browsershot->noSandbox())
            ->name($resultData['fileName'] . '.pdf')
            ->download();
    }

    private function getResultData($resultCacheKey)
    {
        $resultData = Cache::get($resultCacheKey);

           if (!$resultData) {

            abort(419, 'Page expired. Please regenerate the result.');
        }

        $student = $resultData['student'];

        $session = isset($resultData['tableFilters']['school_session_id'])
            ? SchoolSession::find($resultData['tableFilters']['school_session_id'])?->name
            : null;

        $semester = isset($resultData['tableFilters']['semester_id'])
            ? Semester::find($resultData['tableFilters']['semester_id'])?->name
            : null;

        $level = isset($resultData['tableFilters']['level_id'])
            ? Level::find($resultData['tableFilters']['level_id'])?->name
            : null;

        $department = isset($resultData['tableFilters']['department_id'])
            ? Department::find($resultData['tableFilters']['department_id'])?->name
            : null;

        $fileName = 'Result - ' . Str::slug(Str::replace('/', '-', implode(' ', [
            $student->name,
            $session,
            $semester,
            $level,
            $department,
        ])));

        $collegeSettings = app(CollegeSettings::class);

         return (array) $resultData + [
            'session'         => $session,
            'semester'        => $semester,
            'level'           => $level,
            'department'      => $department,
            'fileName'        => $fileName,
            'collegeSettings' => $collegeSettings,
            'student'         => $student
        ];
    }

}

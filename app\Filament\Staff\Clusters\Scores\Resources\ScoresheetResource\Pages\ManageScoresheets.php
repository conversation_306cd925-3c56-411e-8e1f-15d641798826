<?php

namespace App\Filament\Staff\Clusters\Scores\Resources\ScoresheetResource\Pages;

use App\Models\Scoresheet;
use Filament\Actions\CreateAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Clusters\Scores\Resources\ScoresheetResource;

class ManageScoresheets extends ManageRecords
{
    protected static string $resource = ScoresheetResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->before(function (array $data, CreateAction $action): ?array {
                    $exists = Scoresheet::where([
                        'school_session_id' => $data['school_session_id'],
                        'semester_id' => $data['semester_id'],
                        'department_id' => $data['department_id'],
                    ])->exists();

                    if ($exists) {
                        Notification::make()
                            ->danger()
                            ->title('Scoresheet creation failed')
                            ->body('A scoresheet for this registration already exists. Please check and try again.')
                            ->send();

                        return $action->halt();
                    }

                    return $data;
                })
                ->successNotification(function () {
                    return Notification::make()
                        ->success()
                        ->title('Scoresheet Created')
                        ->body('The scoresheet has been created successfully');
                }),
        ];
    }
}

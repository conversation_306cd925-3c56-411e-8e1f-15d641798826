<?php

namespace App\Filament\Staff\Clusters\Scores\Resources;

use App\Models\Semester;
use Filament\Forms\Form;
use App\Models\Scoresheet;
use Filament\Tables\Table;
use App\Models\SchoolSession;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Tables\Filters\Indicator;
use App\Filament\Staff\Clusters\Scores;
use Filament\Forms\Components\Checkbox;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Pages\SubNavigationPosition;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Staff\Clusters\Scores\Resources\ScoresheetResource\Pages;
use App\Filament\Staff\Clusters\Scores\Resources\ScoresheetResource\RelationManagers;

class ScoresheetResource extends Resource
{
    protected static ?string $model = Scoresheet::class;
    protected static ?string $cluster = Scores::class;
    protected static ?int $navigationSort = 2;
    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['department'])
            ->withMax('department as latest_department_name', 'name');
    }

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canCreate(): bool
    {
        return main_staff_access();
    }

    public static function canEdit($record): bool
    {
        return main_staff_access();
    }
    public static function canDelete($record): bool
    {
        return main_staff_access();
    }
    public static function canView($record): bool
    {
        return all_staff_access();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('school_session_id')
                    ->required()
                    ->label('Session')
                    ->native(false)
                    ->relationship('schoolSession', 'name', fn($query) => $query->orderByDesc('name'))
                    ->default(activeSchoolSession()?->id)
                    ->placeholder('Select a session'),
                Select::make('semester_id')
                    ->required()
                    ->label('Semester')
                    ->native(false)
                    ->relationship('semester', 'name')
                    ->default(activeSemester()?->id)
                    ->placeholder('Select a semester'),
                Select::make('department_id')
                    ->required()
                    ->label('Department')
                    ->native(false)
                    ->relationship('department', 'name')
                    ->placeholder('Select a department'),
                Checkbox::make('is_published')
                    ->inline(false)
                    ->label('Publish')
                    ->helperText('Check to publish this scoresheet.'),
            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated(false)
            ->striped()
            ->recordAction(null)
            ->defaultSort('latest_department_name')
            ->emptyStateHeading('No Scoresheets Yet')
            ->emptyStateDescription(new HtmlString('Once you create your first scoresheet, select <b>session</b> and <b>semester</b> at least to view scoresheets.'))
            ->description('Scoresheet allows admins to create sheets for marking and publishing of results.')
            ->persistFiltersInSession()

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('schoolSession.name')
                    ->label('Session'),
                TextColumn::make('latest_department_name')
                    ->label('Department'),
                ToggleColumn::make('is_published')
                    ->label('Published')
                    ->disabled(fn() => ! main_staff_access()),

            ])
            ->filters([
                SelectFilter::make('scoresheet_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->required()
                            ->relationship('schoolSession', 'name', fn($query) => $query->orderByDesc('name'))
                            ->label('Session')
                            ->native(false)
                            ->default(activeSchoolSession()?->id),
                        Select::make('semester_id')
                            ->relationship('semester', 'name')
                            ->label('Semester')
                            ->native(false)
                            ->default(activeSemester()?->id),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, array $data): Builder {
                        if (empty($data['school_session_id']) || empty($data['semester_id'])) {
                            return $query->whereRaw('1 = 0');
                        }

                        return $query;
                    })
                    ->query(
                        fn(Builder $query, array $data) => $query->where('school_session_id', $data['school_session_id'])
                            ->where('semester_id', $data['semester_id'])
                    )
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            if ($name = SchoolSession::find($sessionId)?->name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            if ($name = Semester::find($semesterId)?->name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        return $indicators;
                    })

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn(Action $action) => $action->label('View scoresheets'),
            )
               ->actions([
                 ActionGroup::make([
                    DeleteAction::make()
                            ->successNotification(function () {
                                return Notification::make()
                                    ->success()
                                    ->title('Scoresheet Deleted')
                                    ->body('The scoresheet has been deleted.');
                            })      
                    ])           
               ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageScoresheets::route('/'),
        ];
    }
}

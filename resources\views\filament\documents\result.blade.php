<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>{{ $fileName }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0mm;
            }

            .college-name-print {
                font-size: 20pt !important;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 10px;
            }

            h1 {
                font-size: 16px !important;
            }

            h2 {
                font-size: 14px !important;
            }

            h3 {
                font-size: 12px !important;
            }

            th,
            td {
                font-size: 10px !important;
            }

            .print-grid {
                display: grid !important;
                grid-template-columns: 1fr 1fr !important;
                gap: 1rem;
            }

            th,
            tfoot tr {
                background-color: #f9fafb !important;
                /* <PERSON><PERSON><PERSON>'s gray-50 */
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            * {
                border-color: #6b7280 !important;
                /* gray-500 */
                border-radius: 2px !important;
                /* small radius */
            }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-4xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-header :collegeLogo="asset('images/racoed-favicon.png')" :collegeName="$collegeSettings->name"
            :collegeMotto="$collegeSettings->motto" :collegeAddress="$collegeSettings->address"
            :collegePhone="$collegeSettings->phone" :collegeEmail="$collegeSettings->email"
            :studentPhoto="$student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')"
            heading="Examination & Records" subheading="Student Result Sheet" />

        {{-- STUDENT & ACADEMIC DETAILS --}}
        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Student & Academic Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-1">
                <div class="border p-0.5"><strong>Name:</strong> {{ $student->name ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Session:</strong> {{ $session ?? 'NIL' }}</div>
                <div class="border p-0.5 "><strong>Matric. no.:</strong> {{ $student->matric_number ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Level:</strong> {{ $level ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Phone:</strong> {{ $student->phone ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Semester:</strong> {{ $semester ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Gender:</strong> {{ $student->gender ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Department:</strong> {{ $department ?? 'NIL' }}</div>
            </div>
        </div>

        {{-- RESULT TABLE --}}
        <div class="border p-1">
            <h2 class="text-lg font-semibold mb-2 text-center">Result Sheet</h2>

            <table class="table-auto text-sm border border-gray-300 w-full">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1">#</th>
                        <th class="border px-2 py-1">Course</th>
                        @foreach ($assessmentNames as $assessment => $max_score)
                        <th class="border px-2 py-1 text-center">
                            {{ $assessment }}<br>
                            <span class="block text-xs">({{ $max_score }})</span>
                        </th>
                        @endforeach
                        <th class="border px-2 py-1 text-center">Total<br><span class="block text-xs">({{
                                collect($assessmentNames)->sum() }})</span></th>
                        <th class="border px-2 py-1 text-center">Grade</th>
                        <th class="border px-2 py-1 text-center">Point</th>
                        <th class="border px-2 py-1 text-center">GP</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($courseData as $index => $data)
                    @php
                    $failed = ($data['total_score'] ?? -1) <=
                        \App\Filament\Student\Resources\ResultResource::getFailedScore(); @endphp <tr>
                        <td class="border px-2 py-1 text-center">{{ $index + 1 }}</td>
                        <td class="border px-2 py-1" title="{{ $data['title'] }}">
                            {{ $data['code'] }}
                            <span class="text-gray-400"> {{ $data['credit'] }}{{ $data['status'] }}</span>
                        </td>
                        @foreach ($assessmentNames as $assessment => $max_score)
                        <td class="border px-2 py-1 text-center">{{ $data[$assessment] ?? '-' }}</td>
                        @endforeach
                        <td class="border px-2 py-1 text-center {{ $failed ? 'text-red-500' : 'text-gray-900' }}">
                            {{ $data['total_score'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1 text-center {{ $failed ? 'text-red-500' : 'text-gray-900' }}">
                            {{ $data['grade'] ?? '-' }}
                        </td>
                        <td class="border px-2 py-1 text-center">{{ $data['point'] ?? '-' }}</td>
                        <td class="border px-2 py-1 text-center">{{ $data['grade_point'] ?? '-' }}</td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="{{ 6 + count($assessmentNames) }}" class="text-center py-2">No scores found.
                            </td>
                        </tr>
                        @endforelse
                </tbody>
            </table>
        </div>

        {{-- RESULT SUMMARY --}}
        <div class="border p-1">
            <h2 class="text-lg font-semibold text-center mb-2">Result Summary</h2>

            <h3 class="font-semibold text-center mb-2">Semester</h3>
            <table class="table-auto border border-gray-300 w-full text-center mb-4">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-3 py-2">TCU</th>
                        <th class="border px-3 py-2">TGP</th>
                        <th class="border px-3 py-2">GPA</th>
                        <th class="border px-3 py-2">Remark</th>
                        <th class="border px-3 py-2">Outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border px-3 py-2">{{ $semesterTotalCreditUnit }}</td>
                        <td class="border px-3 py-2">{{ $semesterTotalGradePoint }}</td>
                        <td class="border px-3 py-2">{{ $semesterGradePointAverage ?? '-' }}</td>
                        <td class="border px-3 py-2">{{ $semesterGradeRemark?->remark ?? '-' }}</td>
                        <td class="border px-3 py-2 text-start max-w-[150px]">
                            <div class="overflow-auto max-h-[100px]">
                                {{ $semesterOutstandingCourses->isNotEmpty() ?
                                $semesterOutstandingCourses->pluck('code')->implode(', ') : 'NIL' }}
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>

            <h3 class="font-semibold text-center my-2">Cumulative</h3>
            <table class="table-auto border border-gray-300 w-full text-center">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-3 py-2">CTCU</th>
                        <th class="border px-3 py-2">CTGP</th>
                        <th class="border px-3 py-2">CGPA</th>
                        <th class="border px-3 py-2">C. remark</th>
                        <th class="border px-3 py-2">C. outstanding</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="border px-3 py-2">{{ $cumulativeTotalCreditUnit }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeTotalGradePoint }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeGradePointAverage ?? '-' }}</td>
                        <td class="border px-3 py-2">{{ $cumulativeGradeRemark?->remark ?? '-' }}</td>
                        <td class="border px-3 py-2 text-start max-w-[150px]">
                            <div class="overflow-auto max-h-[100px]">
                                {{ $cumulativeOutstandingCourses->isNotEmpty() ?
                                $cumulativeOutstandingCourses->pluck('code')->implode(', ') : 'NIL' }}
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print result
            </x-filament::button>
        </div>

        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </div>

    @livewireScripts
    @filamentScripts
</body>

</html>
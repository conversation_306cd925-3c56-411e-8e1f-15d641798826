<?php

namespace App\Filament\Staff\Resources\OverviewResource\Pages;

use App\Filament\Staff\Resources\OverviewResource;
use App\Models\Course;
use App\Models\Grade;
use Filament\Actions\Action;
use Filament\Resources\Pages\ManageRecords;
use Filament\Support\Enums\MaxWidth;

class ManageOverview extends ManageRecords
{
    protected static string $resource = OverviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('view')
                ->icon('heroicon-o-eye')
                ->modalContent(fn () => view(
                    'filament.pages.overview',
                    [
                        'tableFilters' => $filters = $this->extractFilters(),
                        'filteredRecords' => $this->getFilteredTableQuery()->get(),
                        'courses' => Course::where([
                            'department_id' => $filters['department_id'] ?? null,
                            'level_id' => $filters['level_id'] ?? null,
                            'semester_id' => $filters['semester_id'] ?? null,
                        ])->get(),
                        'grades' => Grade::orderBy('min_score')->get(),
                        'failedScore' => Grade::where('min_score', 0)->value('max_score') ?? 39,
                    ]
                ))
                ->modalHeading('Overview')
                ->modalSubmitAction(false)
                ->modalCancelActionLabel('Close')
                ->modalWidth(MaxWidth::SevenExtraLarge),
        ];
    }

    private function extractFilters(): array
    {
        $filters = $this->tableFilters['overview_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
        ];
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($title ?? 'Custom Overview View'); ?></title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .header {
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            opacity: 0.9;
            font-size: 0.875rem;
        }
        @media print {
            body { background: white; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1 class="text-2xl font-bold text-gray-900"><?php echo e($title ?? 'Custom Overview View'); ?></h1>
                <p class="text-gray-600 mt-1"><?php echo e($description ?? 'Custom view loaded in a separate window'); ?></p>
                
                <div class="mt-4 no-print">
                    <button onclick="window.print()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mr-2">
                        Print
                    </button>
                    <button onclick="window.close()" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                        Close
                    </button>
                </div>
            </div>

            <div class="grid">
                <div class="stat-card">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">Total Students</div>
                </div>
                
                <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                    <div class="stat-number">567</div>
                    <div class="stat-label">Active Registrations</div>
                </div>
                
                <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                    <div class="stat-number">89%</div>
                    <div class="stat-label">Pass Rate</div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Detailed Information</h2>
            
            <div class="grid">
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Academic Performance</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        This section can contain detailed academic performance metrics, charts, and analysis.
                    </p>
                    
                    <div class="bg-gray-50 p-4 rounded">
                        <h4 class="font-medium mb-2">Grade Distribution</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>A Grade</span>
                                <span class="font-medium">25%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>B Grade</span>
                                <span class="font-medium">35%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>C Grade</span>
                                <span class="font-medium">30%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>D Grade</span>
                                <span class="font-medium">10%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Department Overview</h3>
                    <p class="text-gray-600 text-sm mb-4">
                        Department-wise statistics and performance indicators.
                    </p>
                    
                    <div class="bg-gray-50 p-4 rounded">
                        <h4 class="font-medium mb-2">Top Performing Departments</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span>Computer Science</span>
                                <span class="font-medium text-green-600">92%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Mathematics</span>
                                <span class="font-medium text-green-600">89%</span>
                            </div>
                            <div class="flex justify-between">
                                <span>English</span>
                                <span class="font-medium text-green-600">87%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Custom Content Area</h2>
            <p class="text-gray-600 mb-4">
                This is where you can add any custom content, charts, tables, or interactive elements.
                You have full control over the layout and functionality.
            </p>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            Customization Note
                        </h3>
                        <div class="mt-2 text-sm text-blue-700">
                            <p>
                                Edit this view at <code class="bg-blue-100 px-1 rounded">resources/views/filament/custom/overview-full-page.blade.php</code>
                                to customize the content and layout according to your needs.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/custom/overview-full-page.blade.php ENDPATH**/ ?>
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TotalScore extends Model
{
    /**
     * Get the registration that owns the TotalScore
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function registration()
    {
        return $this->belongsTo(Registration::class);
    }

    /**
     * Get the course that owns the TotalScore
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }
}

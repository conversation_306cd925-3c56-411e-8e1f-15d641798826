<?php

namespace App\Providers\Filament;

use Filament\Panel;
use Illuminate\View\View;
use Filament\PanelProvider;
use Filament\Navigation\MenuItem;
use Illuminate\Support\HtmlString;
use Filament\View\PanelsRenderHook;
use Filament\Support\Enums\MaxWidth;
use Filament\Navigation\NavigationItem;
use Filament\Navigation\NavigationGroup;
use Filament\Http\Middleware\Authenticate;
use App\Filament\Staff\Pages\Auth\StaffProfile;
use Illuminate\Session\Middleware\StartSession;
use Devonab\FilamentEasyFooter\EasyFooterPlugin;
use Illuminate\Cookie\Middleware\EncryptCookies;
use App\Filament\Staff\Pages\Auth\ChangePassword;
use Filament\Http\Middleware\AuthenticateSession;
use Guava\FilamentKnowledgeBase\KnowledgeBasePlugin;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;

class StaffPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->domain(config('custom.portal_url'))
            ->spa()
            ->id('staff')
            ->path('staff')
            ->profile(StaffProfile::class)
            ->sidebarCollapsibleOnDesktop()
            ->sidebarWidth('15rem')
            ->databaseNotifications()
            ->databaseTransactions()
            ->simplePageMaxContentWidth(MaxWidth::FourExtraLarge)

            ->userMenuItems([
                'profile' => MenuItem::make()->label('Bio-data'),
                'logout' => MenuItem::make()->label('Log out'),
                MenuItem::make()
                    ->label('Change password')
                    ->url(fn(): string => ChangePassword::getUrl(['panel' => 'staff']))
                    ->icon('heroicon-o-key')
            ])

            ->navigationGroups([
                NavigationGroup::make()
                    ->label('User')
                    ->icon('heroicon-o-user-group'),

                NavigationGroup::make()
                    ->label('Academic')
                    ->icon('heroicon-o-academic-cap'),

                NavigationGroup::make()
                    ->label('CBTest™')
                    ->icon('heroicon-o-question-mark-circle'),

                NavigationGroup::make()
                    ->label('Result')
                    ->icon('heroicon-o-document-text'),

                NavigationGroup::make()
                    ->label('CPay™')
                    ->icon('heroicon-o-banknotes'),

                NavigationGroup::make()
                    ->label('Website')
                    ->icon('heroicon-o-globe-alt'),
            ])

            ->navigationItems([
                NavigationItem::make('Help')
                    ->url(config('app.url') . '/help', shouldOpenInNewTab: true)
                    ->icon('heroicon-o-question-mark-circle')
                    ->sort(10),
            ])

            ->renderHook(
                PanelsRenderHook::PAGE_START,
                fn(): View => view('filament.hooks.global-bio-data-banner')
            )
            ->renderHook(
                PanelsRenderHook::FOOTER,
                fn(): string => new HtmlString('<span class= "text-center text-gray-500 text-[10px]">Thank you for using <a href="https://www.cportal.ng" target="_blank" rel="noopener noreferrer" class="font-semibold">Cportal</a></span>')
            )

            ->darkMode(false)
            ->brandLogo(asset('images/cportal-logo.png'))
            ->darkModeBrandLogo(asset('images/cportal-logo-light.png'))
            ->brandLogoHeight('2rem')
            ->favicon(asset('images/racoed-favicon.png'))
            ->viteTheme('resources/css/filament/custom/theme.css')

            ->discoverResources(in: app_path('Filament/Staff/Resources'), for: 'App\\Filament\\Staff\\Resources')
            ->discoverClusters(in: app_path('Filament/Staff/Clusters'), for: 'App\\Filament\\Staff\\Clusters')
            ->discoverPages(in: app_path('Filament/Staff/Pages'), for: 'App\\Filament\\Staff\\Pages')
            ->discoverWidgets(in: app_path('Filament/Staff/Widgets'), for: 'App\\Filament\\Staff\\Widgets')

            ->plugins([
                EasyFooterPlugin::make()
                    ->withFooterPosition('footer')
                    ->withBorder()
                    ->hiddenFromPagesEnabled()
                    ->hiddenFromPages(['login', 'forgot-password', 'register', 'reset-password'])
                    ->withSentence('RACOED')
                    ->withGithub(showLogo: false, showUrl: false),
                KnowledgeBasePlugin::make()
                    ->disableKnowledgeBasePanelButton()
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}

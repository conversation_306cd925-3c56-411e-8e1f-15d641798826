<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>Bio-data - {{ $student->name }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0mm;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 10px;
            }

            h1 {
                font-size: 16px !important;
            }

            th,
            td {
                font-size: 10px !important;
            }
        }

        * {
            border-color: #6b7280 !important;
            /* gray-500 */
            border-radius: 2px !important;
            /* small radius */
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-header :collegeLogo="asset('images/racoed-favicon.png')" :collegeName="$collegeSettings->name"
            :collegeMotto="$collegeSettings->motto" :collegeAddress="$collegeSettings->address"
            :collegePhone="$collegeSettings->phone" :collegeEmail="$collegeSettings->email"
            :studentPhoto="$student->photo ? Storage::url($student->photo) : asset('images/placeholder.png')"
            heading="Academic Affairs & Support" subheading="Student Bio-data" />

        {{-- STUDENT DETAILS --}}
        <div class="border p-2">
            <h2 class="text-center font-bold mb-2">Student Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3 gap-1">
                <div class="border p-2"><strong>Name:</strong> {{ $student->name ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Date of birth:</strong> {{ $student->date_of_birth?->format('d M, Y') ??
                    'NIL' }}</div>
                <div class="border p-2"><strong>Gender:</strong> {{ $student->gender ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Marital status:</strong> {{ $student->marital_status ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Religion:</strong> {{ $student->religion ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Nationality:</strong> {{ $student->nationality ?? 'NIL' }}</div>
                <div class="border p-2"><strong>State of origin:</strong> {{ $student->state->name ?? 'NIL' }}</div>
                <div class="border p-2"><strong>LGA:</strong> {{ $student->localGovernmentArea->name ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Phone:</strong> {{ $student->phone ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Email:</strong> {{ $student->email ?? 'NIL' }}</div>
                <div class="border p-2 sm:col-span-2"><strong>Address:</strong> {{ $student->address ?? 'NIL' }}</div>
            </div>
        </div>

        {{-- GUARDIAN DETAILS --}}
        <div class="border p-2">
            <h2 class="text-center font-bold mb-2">Guardian Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3 gap-1">
                <div class="border p-2"><strong>Guardian name:</strong> {{ $student->guardian ?
                    ($student->guardian->title?->getLabel() . ' ' . $student->guardian->name) : 'NIL' }}</div>
                <div class="border p-2"><strong>Relationship:</strong> {{ $student->guardian?->relationship?->getLabel()
                    ?? 'NIL'}}</div>
                <div class="border p-2"><strong>Occupation:</strong> {{ $student->guardian?->occupation?->getLabel() ??
                    'NIL' }}</div>
                <div class="border p-2"><strong>Phone:</strong> {{ $student->guardian?->phone ?? 'NIL'}}</div>
            </div>
        </div>

        {{-- EDUCATIONAL HISTORY --}}
        <div class="border p-2">
            <h2 class="text-center font-bold mb-2">Educational History</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3 gap-1">
                <div class="border p-2"><strong>Secondary school:</strong> {{
                    $student->application?->secondary_school_attended ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Graduation year:</strong> {{
                    $student->application?->secondary_school_graduation_year ?? 'NIL' }}</div>
                <div class="border p-2"><strong>JAMB reg. no.:</strong> {{
                    $student->application?->jamb_registration_number ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Exam board:</strong> {{ $student->application?->exam_board ?? 'NIL' }}
                </div>
            </div>

            {{-- Exam Results Table --}}
            @php
            $results = $student->application?->exam_result ?? [];
            $chunks = count($results) > 0 ? array_chunk($results, ceil(count($results) / 3), true) : [];
            @endphp

            <div class="mt-4">
                <h3 class="font-bold mb-2">Exam Results:</h3>
                <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3 gap-4 text-sm">
                    @foreach ($chunks as $group)
                    <table class="w-full border border-collapse">
                        <thead>
                            <tr>
                                <th class="border px-2 py-1 text-left">Subject</th>
                                <th class="border px-2 py-1 text-left">Grade</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($group as $item)
                            <tr>
                                <td class="border px-2 py-1">{{
                                    \App\Enums\OLevelSubject::tryFrom($item['subject'])?->getLabel() }}</td>
                                <td class="border px-2 py-1">{{ strtoupper($item['grade']) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    @endforeach
                </div>
            </div>

        </div>

        {{-- ACADEMIC DETAILS --}}
        <div class="border rounded p-2">
            <h2 class="text-center font-bold mb-2">Academic Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-3 md:grid-cols-3">
                <div class="border p-2 "><strong>Matric. no.:</strong> {{ $student->matric_number ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Session:</strong> {{ $student->activeRegistration?->SchoolSession->name
                    ?? 'NIL' }}</div>
                <div class="border p-2"><strong>Level:</strong> {{ $student->activeRegistration?->level->name ?? 'NIL'
                    }}</div>
                <div class="border p-2"><strong>Semester:</strong> {{ $student->activeRegistration?->semester->name ??
                    'NIL' }}</div>
                <div class="border p-2 sm:col-span-2"><strong>Programme:</strong> {{
                    $student->activeRegistration?->programme->name ?? 'NIL' }}</div>
            </div>

        </div>

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print bio-data
            </x-filament::button>
        </div>

        <script>
            // Automatically open print dialog when the page loads
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>
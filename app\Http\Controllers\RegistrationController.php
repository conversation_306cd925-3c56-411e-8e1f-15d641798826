<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Department;
use App\Models\Level;
use App\Models\Registration;
use App\Models\SchoolSession;
use App\Models\Semester;
use App\Settings\CollegeSettings;
use Illuminate\Support\Str;
use <PERSON>tie\Browsershot\Browsershot;
use Spatie\LaravelPdf\Facades\Pdf;

class RegistrationController extends Controller
{
    public function print(Registration $registration)
    {
        return view('filament.documents.registration', $this->getRegistrationData($registration));
    }

    public function download(Registration $registration)
    {
        return Pdf::view('filament.documents.registration', $this->getRegistrationData($registration))
            ->withBrowsershot(fn (Browsershot $browsershot) => $browsershot->noSandbox())
            ->name($this->getRegistrationData($registration)['fileName'].'.pdf')
            ->download();
    }

    private function getRegistrationData(Registration $registration)
    {
        $collegeSettings = app(CollegeSettings::class);
        $student = $registration->user;

        $session = SchoolSession::find($registration->school_session_id)?->name;
        $semester = Semester::find($registration->semester_id)?->name;
        $level = Level::find($registration->level_id)?->name;
        $department = Department::find($registration->programme->first_department_id)?->name;

        $fileName = 'Registration - '.Str::slug(Str::replace('/', '-', implode(' ', [
            $student->name,
            $session,
            $semester,
            $level,
            $department,
        ])));

        return [
            'student' => $student,
            'collegeSettings' => $collegeSettings,
            'registration' => $registration,
            'coursesByDepartment' => $this->getCoursesByDepartment($registration),
            'departmentsWithHeads' => $this->getDepartmentsWithHeads($registration),
            'fileName' => $fileName,
        ];
    }

    private function getDepartmentIds(Registration $registration): array
    {
        return array_filter([
            $registration->programme->first_department_id,
            $registration->programme->second_department_id,
            Department::where('is_edu', true)->value('id'),
            Department::where('is_gse', true)->value('id'),
        ]);
    }

    private function getCoursesByDepartment(Registration $registration)
    {
        $deptIds = $this->getDepartmentIds($registration);

        $courses = Course::whereIn('department_id', $deptIds)
            ->where('level_id', $registration->level_id)
            ->where('semester_id', $registration->semester_id)
            ->get()
            ->groupBy('department_id');

        return Department::whereIn('id', $deptIds)
            ->get()
            ->map(fn ($dept) => [
                'department' => $dept,
                'courses' => $courses->get($dept->id, collect()),
            ]);
    }

    private function getDepartmentsWithHeads(Registration $registration)
    {
        $deptIds = $this->getDepartmentIds($registration);

        return Department::with('headOfDepartments')
            ->whereIn('id', $deptIds)
            ->get();
    }
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ asset('images/racoed-favicon.png') }}" type="image/png">
    <title>{{ $fileName }}</title>
    @livewireStyles
    @filamentStyles
    @vite(['resources/css/filament/custom/theme.css', 'resources/css/app.css'])
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0mm;
            }

            .college-name-print {
                font-size: 20pt !important;
            }

            .print\:hidden {
                display: none !important;
            }

            body {
                font-size: 10px;
            }

            h1 {
                font-size: 16px !important;
            }

            th,
            td {
                font-size: 10px !important;
            }

            .print-grid {
                display: grid !important;
                grid-template-columns: repeat(3, 1fr) !important;
            }

            th,
            tfoot tr {
                background-color: #f9fafb !important;
                /* <PERSON>lwind's gray-50 */
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            * {
                border-color: #6b7280 !important;
                /* gray-500 */
                border-radius: 2px !important;
                /* small radius */
            }
        }
    </style>
</head>

<body class="font-sans leading-relaxed p-4">
    <div class="max-w-3xl mx-auto p-4 sm:p-2 text-sm space-y-6">
        {{-- HEADER --}}
        <x-document-simple-header :collegeLogo="asset('images/racoed-favicon.png')"
            :collegeName="$collegeSettings->name" heading="Examination & Records" subheading="Result Scoresheet" />

        {{-- COURSE DETAILS --}}
        <div class="border  p-1">
            <h2 class="text-center font-bold mb-2">Course Details</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 print-grid">
                <div class="border p-0.5"><strong>Session:</strong> {{ $session ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Semester:</strong> {{ $semester ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Level:</strong> {{ $level ?? 'NIL' }}</div>
                <div class="border p-0.5"><strong>Department:</strong> {{ $department ?? 'NIL' }}</div>
                <div class="border p-0.5 sm:col-span-2">
                    <strong>Course:</strong>
                    @if($course)
                    {{ $course->code }}
                    {{ $course->credit ? ' | ' . $course->credit : '' }}
                    {{ $course->course_status ? $course->course_status->getAlias() : '' }}
                    {{ $course->title ? ' | ' . $course->title : '' }}
                    @else
                    NIL
                    @endif
                </div>
            </div>
        </div>

        {{-- COURSE SCORES --}}
        <div class="border  p-1">
            <h2 class="text-center font-bold mb-2">Course Scores</h2>

            <table class="w-full border border-gray-300 text-xs">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="border px-2 py-1 text-center">#</th>
                        <th class="border px-2 py-1 text-left">Name</th>
                        <th class="border px-2 py-1 text-left">Matric. no.</th>
                        @foreach ($assessments as $assessment)
                        <th class="border px-2 py-1 text-center">
                            {{ $assessment->name }}<br>
                            <span class="block text-xs">({{ $assessment->max_score }})</span>
                        </th>
                        @endforeach
                        <th class="border px-2 py-1 text-center">Total<br><span class="block text-xs">({{
                                $assessments->sum('max_score') }})</span></th>
                        <th class="border px-2 py-1 text-center">Grade</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse ($students as $index => $student)
                    <tr>
                        <td class="border px-2 py-1 text-center">{{ $index + 1 }}</td>
                        <td class="border px-2 py-1 text-left">{{ $student['name'] }}</td>
                        <td class="border px-2 py-1 text-left">{{ $student['matric_number'] }}</td>

                        @foreach ($assessments as $assessment)
                        <td class="border px-2 py-1 text-center">
                            @if($blank)
                            {{-- leave empty --}}
                            @else
                            {{ $student['assessmentScores'][$assessment->name] ?? '-' }}
                            @endif
                        </td>
                        @endforeach

                        <td class="border px-2 py-1 text-center">
                            @if($blank)
                            {{-- leave empty --}}
                            @else
                            {{ $student['totalScore'] ?? '-' }}
                            @endif
                        </td>

                        <td class="border px-2 py-1 text-center">
                            @if($blank)
                            {{-- leave empty --}}
                            @else
                            {{ $student['grade'] ?? '-' }}
                            @endif
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="{{ 4 + count($assessments) }}" class="border px-2 py-2 text-center">No students
                            found</td>
                    </tr>
                    @endforelse
                </tbody>

            </table>
        </div>

        {{--SCORES SUMMARY --}}
        @if(!$blank && count($students) > 0 && isset($grades))
        @php
        $totalStudents = count($students);
        $gradeStats = [];
        $passCount = 0;
        $failCount = 0;
        $noGradeCount = 0; // Track students with no grade

        foreach($grades as $grade) {
        $gradeStats[$grade->name] = 0;
        }

        $passingGrades = $grades->where('min_score', '>=', 40)->pluck('name')->toArray();

        foreach($students as $student) {
        $studentGrade = $student['grade'] ?? null;

        if($studentGrade && isset($gradeStats[$studentGrade])) {
        $gradeStats[$studentGrade]++;

        // Only count as pass/fail if student has a grade
        if(in_array($studentGrade, $passingGrades)) {
        $passCount++;
        } else {
        $failCount++;
        }
        } else {
        // Student has no grade
        $noGradeCount++;
        }
        }

        // Calculate percentages based on students with grades only
        $studentsWithGrades = $totalStudents - $noGradeCount;
        $passPercentage = $studentsWithGrades > 0 ? round(($passCount / $studentsWithGrades) * 100, 1) : 0;
        $failPercentage = $studentsWithGrades > 0 ? round(($failCount / $studentsWithGrades) * 100, 1) : 0;
        @endphp

        <div class="border p-1">
            <h2 class="text-center font-bold mb-2">Scores Summary</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 print-grid">

                <div class="border p-1">
                    <strong>Total Students:</strong> {{ $totalStudents }}
                </div>

                <div class="border p-1">
                    <strong>Passed:</strong> {{ $passCount }} ({{ $passPercentage }}%)
                </div>

                <div class="border p-1">
                    <strong>Failed:</strong> {{ $failCount }} ({{ $failPercentage }}%)
                </div>

                @if($noGradeCount > 0)
                <div class="border p-1">
                    <strong>No Grade:</strong> {{ $noGradeCount }}
                </div>
                @endif

                @foreach($grades as $grade)
                @if($gradeStats[$grade->name] > 0)
                <div class="border p-1">
                    <strong>Grade {{ $grade->name }}:</strong> {{ $gradeStats[$grade->name] }}
                    ({{ $studentsWithGrades > 0 ? round(($gradeStats[$grade->name] / $studentsWithGrades) * 100, 1) : 0
                    }}%)
                </div>
                @endif
                @endforeach
            </div>
        </div>
        @endif

        {{-- LECTURER IN CHARGE--}}
        <div class="mt-6">
            <p class="font-bold">Lecturer in Charge</p>
            <p><span class="font-bold">Name:</span> ____________________________________</p>
            <p><span class="font-bold">Sign & Date:</span> _____________________________</p>
        </div>

        {{-- PRINT BUTTON --}}
        <div class="fixed bottom-4 right-4 print:hidden">
            <x-filament::button tag="button" color="primary" icon="heroicon-o-printer" onclick="window.print()">
                Print scoresheet
            </x-filament::button>
        </div>

        <script>
            window.onload = function() {
                window.print();
            }
        </script>
    </div>
</body>

</html>
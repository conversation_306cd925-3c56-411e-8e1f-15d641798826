<div class="p-6">
    <div class="mb-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Custom Overview View
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            This is a custom blade view loaded in a modal
        </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="font-medium text-gray-900 dark:text-white mb-2">Section 1</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                Add your custom content here. You can include charts, tables, or any other content.
            </p>
        </div>

        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="font-medium text-gray-900 dark:text-white mb-2">Section 2</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
                This view has access to all Laravel features and can include dynamic content.
            </p>
        </div>
    </div>

    <div class="mt-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Information
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <p>
                            You can customize this view by editing the blade file at 
                            <code class="bg-blue-100 dark:bg-blue-800 px-1 rounded">resources/views/filament/custom/overview-custom-view.blade.php</code>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Herd\racoed\resources\views/filament/custom/overview-custom-view.blade.php ENDPATH**/ ?>
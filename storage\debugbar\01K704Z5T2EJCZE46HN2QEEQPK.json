{"__meta": {"id": "01K704Z5T2EJCZE46HN2QEEQPK", "datetime": "2025-10-07 21:14:25", "utime": **********.602877, "method": "GET", "uri": "/_ignition/health-check", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[21:14:25] LOG.warning: Since symfony/console 7.3: Method \"Symfony\\Component\\Console\\Command\\Command::getDefaultDescription()\" is deprecated and will be removed in Symfony 8.0, use the #[AsCommand] attribute instead. in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\symfony\\deprecation-contracts\\function.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": **********.166301, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.670922, "end": **********.602905, "duration": 0.9319829940795898, "duration_str": "932ms", "measures": [{"label": "Booting", "start": **********.670922, "relative_start": 0, "end": **********.053694, "relative_end": **********.053694, "duration": 0.*****************, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.053713, "relative_start": 0.*****************, "end": **********.602908, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "549ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.057291, "relative_start": 0.***************, "end": **********.061272, "relative_end": **********.061272, "duration": 0.003980875015258789, "duration_str": "3.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.601458, "relative_start": 0.****************, "end": **********.601969, "relative_end": **********.601969, "duration": 0.0005109310150146484, "duration_str": "511μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.602002, "relative_start": 0.****************, "end": **********.602039, "relative_end": **********.602039, "duration": 3.719329833984375e-05, "duration_str": "37μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7097704, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "uri": "GET _ignition/health-check", "controller": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "prefix": "_ignition", "middleware": "Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled", "duration": "929ms", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-129823669 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Imsxc0Z4QlFRbWw5aHQzckhOYUlMY2c9PSIsInZhbHVlIjoiY2RUQnp4OTRQNmpxM2tyTm9yd3hha09XU0prODFBam9qWTdKWHQ4bnB2OTNsZVZRbUlHRUJOR0x6Wk1lK3MzSVlSazFZaUZiMjFlK0RyWHhrWGZjd2NkaDJVSzJzRENTZ0RtN29pTjJwUHVTQVJMZ2l2M2NKUDhhOEp4NXZ6RkgiLCJtYWMiOiI2NzExMGU2NDJkZGU1ZDRkMGU1ODliMmE4YmQxMjVlMmM1ZDE3YTQ1Zjk4ZmQ4ZWEyOTg3NzE5N2ZmM2EzZDg3IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IjZ2VEtEQVRra1p4eUY0VnNFUGEvaGc9PSIsInZhbHVlIjoiV3VQOXNRWWhlL3dUSXEyZnUwa0M5RlExSnI5NVIxOGdTbUVLajZxcy9mSnhrUG1yREFiVVhKbVRtV2grdk4rcklWMXJTOEFCM3V5b1ZwYmlseFpVWEcxS1BQREVqT1diajJRSXk5bUlTQmFrMkJaeXh6MWRjRjNXN1loREtPazIiLCJtYWMiOiI1NzM3ZjEzZmMyOTFjZWU0YTRmZjA0ZGZmYjBkZDI4NWVhYTgxZWYwODZmNDhhNjlhOWY3ZTg3MjkyMjc1YWMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"228 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=2&amp;tableFilters[overview_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-129823669\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-971764253 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Imsxc0Z4QlFRbWw5aHQzckhOYUlMY2c9PSIsInZhbHVlIjoiY2RUQnp4OTRQNmpxM2tyTm9yd3hha09XU0prODFBam9qWTdKWHQ4bnB2OTNsZVZRbUlHRUJOR0x6Wk1lK3MzSVlSazFZaUZiMjFlK0RyWHhrWGZjd2NkaDJVSzJzRENTZ0RtN29pTjJwUHVTQVJMZ2l2M2NKUDhhOEp4NXZ6RkgiLCJtYWMiOiI2NzExMGU2NDJkZGU1ZDRkMGU1ODliMmE4YmQxMjVlMmM1ZDE3YTQ1Zjk4ZmQ4ZWEyOTg3NzE5N2ZmM2EzZDg3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjZ2VEtEQVRra1p4eUY0VnNFUGEvaGc9PSIsInZhbHVlIjoiV3VQOXNRWWhlL3dUSXEyZnUwa0M5RlExSnI5NVIxOGdTbUVLajZxcy9mSnhrUG1yREFiVVhKbVRtV2grdk4rcklWMXJTOEFCM3V5b1ZwYmlseFpVWEcxS1BQREVqT1diajJRSXk5bUlTQmFrMkJaeXh6MWRjRjNXN1loREtPazIiLCJtYWMiOiI1NzM3ZjEzZmMyOTFjZWU0YTRmZjA0ZGZmYjBkZDI4NWVhYTgxZWYwODZmNDhhNjlhOWY3ZTg3MjkyMjc1YWMxIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971764253\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-655259927 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 20:14:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655259927\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14797808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-14797808\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController"}, "badge": null}}
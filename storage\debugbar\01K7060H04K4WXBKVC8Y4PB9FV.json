{"__meta": {"id": "01K7060H04K4WXBKVC8Y4PB9FV", "datetime": "2025-10-07 21:32:38", "utime": **********.404528, "method": "GET", "uri": "/_ignition/health-check", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[21:32:38] LOG.warning: Since symfony/console 7.3: Method \"Symfony\\Component\\Console\\Command\\Command::getDefaultDescription()\" is deprecated and will be removed in Symfony 8.0, use the #[AsCommand] attribute instead. in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\symfony\\deprecation-contracts\\function.php on line 25", "message_html": null, "is_string": false, "label": "warning", "time": **********.230808, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.937563, "end": **********.404549, "duration": 0.46698594093322754, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.937563, "relative_start": 0, "end": **********.223347, "relative_end": **********.223347, "duration": 0.****************, "duration_str": "286ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.223369, "relative_start": 0.*****************, "end": **********.404552, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.225383, "relative_start": 0.*****************, "end": **********.2276, "relative_end": **********.2276, "duration": 0.0022170543670654297, "duration_str": "2.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.403827, "relative_start": 0.***************, "end": **********.404076, "relative_end": **********.404076, "duration": 0.0002491474151611328, "duration_str": "249μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.40412, "relative_start": 0.*****************, "end": **********.404147, "relative_end": **********.404147, "duration": 2.6941299438476562e-05, "duration_str": "27μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7097704, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "uri": "GET _ignition/health-check", "controller": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController", "prefix": "_ignition", "middleware": "Spatie\\LaravelIgnition\\Http\\Middleware\\RunnableSolutionsEnabled", "duration": "464ms", "peak_memory": "10MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-579495501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-579495501\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-134403095 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-134403095\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImtsTm81cWlqSmpZODJMNTBDNFhydWc9PSIsInZhbHVlIjoiZ2pXSUp6TWpPNVdlTnVFMFJLclpSZFBGSmVOSTNJK2Z4aHJhd2VDZ3BScEI2dnYwamd5c2Q0UTlPN3ZHUnVNY3VLNU54RUdwNEowK3dSNEF2REtRSFVHaFFvNEs0QmdQSkFCTFVIektLUnpnSHVNUDhnbmtGdDdITVlRQXdZNDEiLCJtYWMiOiI5OTUzYTY2Y2FmMmM4YzYxN2ZhYjcyNTYyNTNlNTYzMjRiNTViNmMwODU0ZjgyZWMwZjUzZGMxNzhkMjMzYTExIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6ImVHQk0rWXFtUDhQckVZMEYwVU9FblE9PSIsInZhbHVlIjoiQ0owY2c5SXhneWd4SUh1TWVDYkpLd2Irc2ovRVZsUk9LUndpR3BUeFprd2pTUmJPYlF0QWdEcjNEejlqdVZFTUFhUUl1ZlNRazhrSDVLTzRMY2hLNTQwbWc3VDZyWVJqNUp5YXBWVEp4R2FXNURwcURBZUJTMGk0MlBFSUFpUnQiLCJtYWMiOiI5MjEwMDIyZmRkMzA5Njg0OTIyMjMxMzY4NjBiYWI1ZmY5NDAzMjlhMTRhNmY2MDU2YjA2YzYyY2U4MDJkNjY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"228 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=2&amp;tableFilters[overview_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2019310139 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImtsTm81cWlqSmpZODJMNTBDNFhydWc9PSIsInZhbHVlIjoiZ2pXSUp6TWpPNVdlTnVFMFJLclpSZFBGSmVOSTNJK2Z4aHJhd2VDZ3BScEI2dnYwamd5c2Q0UTlPN3ZHUnVNY3VLNU54RUdwNEowK3dSNEF2REtRSFVHaFFvNEs0QmdQSkFCTFVIektLUnpnSHVNUDhnbmtGdDdITVlRQXdZNDEiLCJtYWMiOiI5OTUzYTY2Y2FmMmM4YzYxN2ZhYjcyNTYyNTNlNTYzMjRiNTViNmMwODU0ZjgyZWMwZjUzZGMxNzhkMjMzYTExIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVHQk0rWXFtUDhQckVZMEYwVU9FblE9PSIsInZhbHVlIjoiQ0owY2c5SXhneWd4SUh1TWVDYkpLd2Irc2ovRVZsUk9LUndpR3BUeFprd2pTUmJPYlF0QWdEcjNEejlqdVZFTUFhUUl1ZlNRazhrSDVLTzRMY2hLNTQwbWc3VDZyWVJqNUp5YXBWVEp4R2FXNURwcURBZUJTMGk0MlBFSUFpUnQiLCJtYWMiOiI5MjEwMDIyZmRkMzA5Njg0OTIyMjMxMzY4NjBiYWI1ZmY5NDAzMjlhMTRhNmY2MDU2YjA2YzYyY2U4MDJkNjY3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2019310139\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-524070299 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 20:32:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-524070299\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-696125572 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-696125572\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/_ignition/health-check", "action_name": "ignition.healthCheck", "controller_action": "Spatie\\LaravelIgnition\\Http\\Controllers\\HealthCheckController"}, "badge": null}}
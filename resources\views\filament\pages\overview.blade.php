@php
use App\Models\Course;
use App\Models\Grade;
use App\Models\Scoresheet;
use App\Models\TotalScore;

$totalCreditUnit = $courses->sum('credit');
$grades = Grade::all();
$failedScore = Grade::where('min_score', 0)->value('max_score') ?? 39;
@endphp

<div class="p-6">
    <div class="overflow-x-auto">
        <table class="table-auto text-sm border border-gray-300 w-full">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Matric no.</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Semester summary</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Cumulative summary</th>
                </tr>
                <tr class="bg-gray-50">
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="border px-2 py-1 text-center">Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Carry-over courses">Outstanding</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Remark">C. Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Outstanding Courses">C.
                        Outstanding</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($filteredRecords as $index => $record)
                @php
                $registration = $record->registrations->first();

                if (!$registration) {
                $gpa = 0;
                $remark = null;
                $outstandingCourses = 'NIL';
                $cgpa = 0;
                $cumulativeRemark = null;
                $cumulativeOutstanding = 'NIL';
                } else {
                // Semester calculations
                $totalGradePoint = 0;
                $outstanding = [];

                foreach ($registration->totalScores as $score) {
                $grade = $grades->first(fn($g) => $score->total >= $g->min_score && $score->total <= $g->max_score);

                    if ($grade && $score->course) {
                    $totalGradePoint += $grade->point * $score->course->credit;
                    }

                    if ($score->total <= $failedScore && $score->course) {
                        $outstanding[] = $score->course->code;
                        }
                        }

                        $gpa = $totalCreditUnit > 0 ? number_format($totalGradePoint / $totalCreditUnit, 2) : 0;
                        $remark = $grades->first(fn($g) => $gpa >= $g->min_point && $gpa <= $g->max_point);
                            $outstandingCourses = $outstanding ? implode(', ', $outstanding) : 'NIL';

                            // Cumulative calculations following OverviewResource pattern
                            $registrations = $record->registrations;

                            // Collect all registered combinations of level, semester, and session
                            $registeredCombos = $registrations->map(function ($reg) {
                            return [
                            'level_id' => $reg->level_id,
                            'semester_id' => $reg->semester_id,
                            'school_session_id' => $reg->school_session_id,
                            ];
                            });

                            // Filter only those combinations where result is published
                            $publishedCombos = $registeredCombos->filter(function ($combo) use ($tableFilters) {
                            return Scoresheet::where([
                            'semester_id' => $combo['semester_id'],
                            'school_session_id' => $combo['school_session_id'],
                            'department_id' => $tableFilters['department_id'] ?? null,
                            'is_published' => true,
                            ])->exists();
                            })->values();

                            if ($publishedCombos->isEmpty()) {
                            $cgpa = 0;
                            $cumulativeRemark = null;
                            $cumulativeOutstanding = 'NIL';
                            } else {
                            // Fetch courses only for those published level + semester combos
                            $cumulativeCourses = Course::select('id', 'code', 'level_id', 'semester_id', 'credit')
                            ->where('department_id', $tableFilters['department_id'] ?? null)
                            ->where(function ($query) use ($publishedCombos) {
                            foreach ($publishedCombos as $combo) {
                            $query->orWhere(function ($sub) use ($combo) {
                            $sub->where('level_id', $combo['level_id'])
                            ->where('semester_id', $combo['semester_id']);
                            });
                            }
                            })
                            ->get();

                            $cumulativeTotalCreditUnit = $cumulativeCourses->sum('credit');
                            $cumulativeTotalGradePoint = 0;
                            $cumulativeOutstandingList = [];

                            // Calculate cumulative grade points and outstanding courses
                            foreach ($cumulativeCourses as $course) {
                            $combo = $publishedCombos->first(function ($item) use ($course) {
                            return $item['level_id'] == $course->level_id && $item['semester_id'] ==
                            $course->semester_id;
                            });

                            if (!$combo) {
                            continue;
                            }

                            $registration = $record->registrations->first(function ($reg) use ($combo) {
                            return $reg->level_id == $combo['level_id'] &&
                            $reg->semester_id == $combo['semester_id'] &&
                            $reg->school_session_id == $combo['school_session_id'];
                            });

                            if (!$registration) {
                            continue;
                            }

                            $totalScore = TotalScore::where([
                            'registration_id' => $registration->id,
                            'course_id' => $course->id,
                            ])->value('total');

                            if ($totalScore !== null) {
                            $grade = $grades->first(fn($g) => $totalScore >= $g->min_score && $totalScore <= $g->
                                max_score);

                                if ($grade) {
                                $cumulativeTotalGradePoint += $grade->point * $course->credit;
                                }

                                if ($totalScore <= $failedScore) { $cumulativeOutstandingList[]=$course->code;
                                    }
                                    }
                                    }

                                    $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
                                    $cumulativeTotalCreditUnit, 2) : 0;
                                    $cumulativeRemark = $grades->first(fn($g) => $cgpa >= $g->min_point && $cgpa <= $g->
                                        max_point);
                                        $cumulativeOutstanding = $cumulativeOutstandingList ? implode(', ',
                                        array_unique($cumulativeOutstandingList)) : 'NIL';
                                        }
                                        }
                                        @endphp
                                        <tr>
                                            <td class="border px-2 py-1 ">{{ $index + 1 }}</td>
                                            <td class="border px-2 py-1">{{ $record->name }}</td>
                                            <td class="border px-2 py-1">{{ $record->matric_number }}</td>
                                            <td class="border px-2 py-1 ">{{ $gpa }}</td>
                                            <td class="border px-2 py-1 ">{{ $remark?->remark ?? '-' }}</td>
                                            <td class="border px-2 py-1 ">
                                                <div class="max-w-[150px] overflow-auto max-h-[50px]">
                                                    {{ $outstandingCourses }}
                                                </div>
                                            </td>
                                            <td class="border px-2 py-1 ">{{ $cgpa }}</td>
                                            <td class="border px-2 py-1 ">{{ $cumulativeRemark?->remark ??
                                                '-' }}</td>
                                            <td class="border px-2 py-1 ">
                                                <div class="max-w-[150px] overflow-auto max-h-[50px]">
                                                    {{ $cumulativeOutstanding }}
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="9" class="border px-2 py-1 text-center">No students found.</td>
                                        </tr>
                                        @endforelse
            </tbody>
        </table>
    </div>
</div>
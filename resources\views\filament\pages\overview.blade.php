@php
use App\Filament\Staff\Resources\OverviewResource;

// Get courses for the current semester
$courses = OverviewResource::getCourses($this);

// Calculate semester and cumulative totals for header display
$semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses);
// For cumulative, we'll use a sample record to get the total (all students should have same CTCU for same filters)
$sampleRecord = $filteredRecords->first();
$cumulativeTotalCreditUnit = $sampleRecord ? OverviewResource::getCumulativeTotalCreditUnit($this, $sampleRecord) : 0;
@endphp

<div class="p-6">
    <div class="overflow-x-auto">
        <table class="table-auto text-sm border border-gray-300 w-full">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Matric no.</th>
                    <th class="border px-2 py-1 text-center" colspan="4" x-tooltip.raw="Total Credit Unit">Semester
                        summary (TCU: {{
                        $semesterTotalCreditUnit }})</th>
                    <th class="border px-2 py-1 text-center" colspan="4" x-tooltip.raw="Cumulative Total Credit Unit">
                        Cumulative summary (CTCU: {{
                        $cumulativeTotalCreditUnit }})</th>
                    @foreach ($courses as $course)
                    <th class="border px-2 py-1 text-center" colspan="4" x-tooltip.raw="{{ $course->title }}">
                        {{ $course->code }}
                        <span class="text-gray-400">{{ $course->credit }}{{ $course->course_status->value }}</span>
                    </th>
                    @endforeach
                </tr>
                <tr class="bg-gray-50">
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Total Grade Point">TGP</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="border px-2 py-1 text-center">Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Carry-over courses">Outstanding</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Total Grade Point">CTGP</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Remark">C. Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Outstanding Courses">C.
                        Outstanding</th>
                    @foreach ($courses as $course)
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Score">S</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade">G</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Point">P</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point">GP</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
                @forelse ($filteredRecords as $index => $record)
                @php
                // Semester calculations using OverviewResource methods
                $semesterCourses = OverviewResource::getCourses($this);
                $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($semesterCourses);
                $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePoint($this, $record,
                $semesterCourses);
                $gpa = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit,
                2) : 0;
                $remark = OverviewResource::getRemarkFromGradePointAverage($gpa);
                $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCourses($this, $record,
                $semesterCourses);
                $outstandingCourses = $semesterOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';

                // Cumulative calculations using OverviewResource methods
                $cumulativeTotalCreditUnit = OverviewResource::getCumulativeTotalCreditUnit($this, $record);
                $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePoint($this, $record);
                $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
                $cumulativeTotalCreditUnit, 2) : 0;
                $cumulativeRemark = OverviewResource::getRemarkFromGradePointAverage($cgpa);
                $cumulativeOutstandingCourses = OverviewResource::getCumulativeOutstandingCourses($record, $this);
                $cumulativeOutstanding = $cumulativeOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';
                @endphp
                <tr>
                    <td class="border px-2 py-1 ">{{ $index + 1 }}</td>
                    <td class="border px-2 py-1">{{ $record->name }}</td>
                    <td class="border px-2 py-1">{{ $record->matric_number }}</td>
                    <td class="border px-2 py-1 ">{{ $semesterTotalGradePoint }}</td>
                    <td class="border px-2 py-1 ">{{ $gpa }}</td>
                    <td class="border px-2 py-1 ">{{ $remark?->remark ?? '-' }}</td>
                    <td class="border px-2 py-1">
                        <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">
                            {{ $outstandingCourses }}
                        </div>
                    </td>
                    <td class="border px-2 py-1 ">{{ $cumulativeTotalGradePoint }}</td>
                    <td class="border px-2 py-1 ">{{ $cgpa }}</td>
                    <td class="border px-2 py-1 ">{{ $cumulativeRemark?->remark ?? '-' }}</td>
                    <td class="border px-2 py-1">
                        <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">
                            {{ $cumulativeOutstanding }}
                        </div>
                    </td>
                    @foreach ($courses as $course)
                    @php
                    $courseData = OverviewResource::getCourseData($this, $record, $course);
                    $failedScore = OverviewResource::getFailedScore();
                    @endphp
                    <td
                        class="border px-2 py-1 text-center {{ ($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : '' }}">
                        {{ $courseData['total_score'] ?? '-' }}
                    </td>
                    <td
                        class="border px-2 py-1 text-center {{ ($courseData['total_score'] ?? -1) <= $failedScore ? 'text-red-600' : '' }}">
                        {{ $courseData['grade'] ?? '-' }}
                    </td>
                    <td class="border px-2 py-1 text-center">
                        {{ $courseData['point'] ?? '-' }}
                    </td>
                    <td class="border px-2 py-1 text-center">
                        {{ $courseData['grade_point'] ?? '-' }}
                    </td>
                    @endforeach
                </tr>
                @empty
                <tr>
                    <td colspan="{{ 11 + ($courses->count() * 4) }}" class="border px-2 py-1 text-center">No students
                        found.</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
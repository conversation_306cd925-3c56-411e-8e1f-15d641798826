@php
use App\Models\Course;
use App\Models\Grade;
use App\Models\Scoresheet;

$totalCreditUnit = $courses->sum('credit');
$grades = Grade::all();
$failedScore = Grade::where('min_score', 0)->value('max_score') ?? 39;
@endphp

<div class="p-6">
    <div class="overflow-x-auto">
        <table class="table-auto text-sm border border-gray-300 w-full">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Matric no.</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Semester summary</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Cumulative summary</th>
                </tr>
                <tr class="bg-gray-50">
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="border px-2 py-1 text-center">Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Carry-over courses">Outstanding</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Remark">C. Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Outstanding Courses">C.
                        Outstanding</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($filteredRecords as $index => $record)
                @php
                $registration = $record->registrations->first();

                if (!$registration) {
                $gpa = 0;
                $remark = null;
                $outstandingCourses = 'NIL';
                $cgpa = 0;
                $cumulativeRemark = null;
                $cumulativeOutstanding = 'NIL';
                } else {
                // Semester calculations
                $totalGradePoint = 0;
                $outstanding = [];

                foreach ($registration->totalScores as $score) {
                $grade = $grades->first(fn($g) => $score->total >= $g->min_score && $score->total <= $g->max_score);

                    if ($grade && $score->course) {
                    $totalGradePoint += $grade->point * $score->course->credit;
                    }

                    if ($score->total <= $failedScore && $score->course) {
                        $outstanding[] = $score->course->code;
                        }
                        }

                        $gpa = $totalCreditUnit > 0 ? number_format($totalGradePoint / $totalCreditUnit, 2) : 0;
                        $remark = $grades->first(fn($g) => $gpa >= $g->min_point && $gpa <= $g->max_point);
                            $outstandingCourses = $outstanding ? implode(', ', $outstanding) : 'NIL';

                            // Cumulative calculations
                            $cumulativeTotalGradePoint = 0;
                            $cumulativeTotalCreditUnit = 0;
                            $cumulativeOutstandingList = [];

                            foreach ($record->registrations as $reg) {
                            // Check if scoresheet is published for this registration
                            $isPublished = Scoresheet::where([
                            'semester_id' => $reg->semester_id,
                            'school_session_id' => $reg->school_session_id,
                            'department_id' => $tableFilters['department_id'] ?? null,
                            'is_published' => true,
                            ])->exists();

                            if (!$isPublished) {
                            continue;
                            }

                            // Get courses for this registration
                            $regCourses = Course::select('id', 'code', 'credit')
                            ->where('level_id', $reg->level_id)
                            ->where('semester_id', $reg->semester_id)
                            ->where('department_id', $tableFilters['department_id'] ?? null)
                            ->get();

                            $cumulativeTotalCreditUnit += $regCourses->sum('credit');

                            foreach ($reg->totalScores as $score) {
                            $grade = $grades->first(fn($g) => $score->total >= $g->min_score && $score->total <= $g->
                                max_score);

                                if ($grade && $score->course) {
                                $cumulativeTotalGradePoint += $grade->point * $score->course->credit;
                                }

                                if ($score->total <= $failedScore && $score->course) {
                                    $cumulativeOutstandingList[] = $score->course->code;
                                    }
                                    }
                                    }

                                    $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
                                    $cumulativeTotalCreditUnit, 2) : 0;
                                    $cumulativeRemark = $grades->first(fn($g) => $cgpa >= $g->min_point && $cgpa <= $g->
                                        max_point);
                                        $cumulativeOutstanding = $cumulativeOutstandingList ? implode(', ',
                                        array_unique($cumulativeOutstandingList)) : 'NIL';
                                        }
                                        @endphp
                                        <tr>
                                            <td class="border px-2 py-1 text-center">{{ $index + 1 }}</td>
                                            <td class="border px-2 py-1">{{ $record->name }}</td>
                                            <td class="border px-2 py-1">{{ $record->matric_number }}</td>
                                            <td class="border px-2 py-1 text-center">{{ $gpa }}</td>
                                            <td class="border px-2 py-1 text-center">{{ $remark?->remark ?? '-' }}</td>
                                            <td class="border px-2 py-1 text-center">
                                                <div class="max-w-[150px] overflow-auto max-h-[50px]">
                                                    {{ $outstandingCourses }}
                                                </div>
                                            </td>
                                            <td class="border px-2 py-1 text-center">{{ $cgpa }}</td>
                                            <td class="border px-2 py-1 text-center">{{ $cumulativeRemark?->remark ??
                                                '-' }}</td>
                                            <td class="border px-2 py-1 text-center">
                                                <div class="max-w-[150px] overflow-auto max-h-[50px]">
                                                    {{ $cumulativeOutstanding }}
                                                </div>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="9" class="border px-2 py-1 text-center">No students found.</td>
                                        </tr>
                                        @endforelse
            </tbody>
        </table>
    </div>
</div>
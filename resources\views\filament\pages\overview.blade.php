@php
use App\Filament\Staff\Resources\OverviewResource;

@endphp

<div class="p-6">
    <div class="overflow-x-auto">
        <table class="table-auto text-sm border border-gray-300 w-full">
            <thead class="bg-gray-100">
                <tr>
                    <th class="border px-2 py-1">#</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Matric no.</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Semester summary</th>
                    <th class="border px-2 py-1 text-center" colspan="3">Cumulative summary</th>
                </tr>
                <tr class="bg-gray-50">
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1"></th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Grade Point Average">GPA</th>
                    <th class="border px-2 py-1 text-center">Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Carry-over courses">Outstanding</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Grade Point Average">CGPA</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Remark">C. Remark</th>
                    <th class="border px-2 py-1 text-center" x-tooltip.raw="Cumulative Outstanding Courses">C.
                        Outstanding</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($filteredRecords as $index => $record)
                @php
                // Semester calculations using OverviewResource methods
                $semesterCourses = OverviewResource::getCourses($this);
                $semesterTotalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($semesterCourses);
                $semesterTotalGradePoint = OverviewResource::getSemesterTotalGradePoint($this, $record,
                $semesterCourses);
                $gpa = $semesterTotalCreditUnit > 0 ? number_format($semesterTotalGradePoint / $semesterTotalCreditUnit,
                2) : 0;
                $remark = OverviewResource::getRemarkFromGradePointAverage($gpa);
                $semesterOutstandingCourses = OverviewResource::getSemesterOutstandingCourses($this, $record,
                $semesterCourses);
                $outstandingCourses = $semesterOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';

                // Cumulative calculations using OverviewResource methods
                $cumulativeTotalCreditUnit = OverviewResource::getCumulativeTotalCreditUnit($this, $record);
                $cumulativeTotalGradePoint = OverviewResource::getCumulativeTotalGradePoint($this, $record);
                $cgpa = $cumulativeTotalCreditUnit > 0 ? number_format($cumulativeTotalGradePoint /
                $cumulativeTotalCreditUnit, 2) : 0;
                $cumulativeRemark = OverviewResource::getRemarkFromGradePointAverage($cgpa);
                $cumulativeOutstandingCourses = OverviewResource::getCumulativeOutstandingCourses($record, $this);
                $cumulativeOutstanding = $cumulativeOutstandingCourses->pluck('code')->implode(', ') ?: 'NIL';
                @endphp
                <tr>
                    <td class="border px-2 py-1 ">{{ $index + 1 }}</td>
                    <td class="border px-2 py-1">{{ $record->name }}</td>
                    <td class="border px-2 py-1">{{ $record->matric_number }}</td>
                    <td class="border px-2 py-1 ">{{ $gpa }}</td>
                    <td class="border px-2 py-1 ">{{ $remark?->remark ?? '-' }}</td>
                    <td class="border px-2 py-1">
                        <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">
                            {{ $outstandingCourses }}
                        </div>
                    </td>
                    <td class="border px-2 py-1 ">{{ $cgpa }}</td>
                    <td class="border px-2 py-1 ">{{ $cumulativeRemark?->remark ?? '-' }}</td>
                    <td class="border px-2 py-1">
                        <div class="max-w-[150px] overflow-x-auto whitespace-nowrap">
                            {{ $cumulativeOutstanding }}
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="9" class="border px-2 py-1 text-center">No students found.</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
{"__meta": {"id": "01K6YVP13CVZB6AC7AXSK0RVDF", "datetime": "2025-10-07 09:12:54", "utime": **********.253158, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 4, "messages": [{"message": "[09:12:53] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php on line 100", "message_html": null, "is_string": false, "label": "warning", "time": 1759824773.127159, "xdebug_link": null, "collector": "log"}, {"message": "[09:12:53] LOG.warning: explode(): Passing null to parameter #2 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php on line 100", "message_html": null, "is_string": false, "label": "warning", "time": 1759824773.337602, "xdebug_link": null, "collector": "log"}, {"message": "[09:12:54] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.232794, "xdebug_link": null, "collector": "log"}, {"message": "[09:12:54] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.233152, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.099619, "end": **********.253203, "duration": 3.1535840034484863, "duration_str": "3.15s", "measures": [{"label": "Booting", "start": **********.099619, "relative_start": 0, "end": **********.888109, "relative_end": **********.888109, "duration": 0.****************, "duration_str": "788ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.888136, "relative_start": 0.****************, "end": **********.253206, "relative_end": 3.0994415283203125e-06, "duration": 2.***************, "duration_str": "2.37s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.910074, "relative_start": 0.****************, "end": **********.912506, "relative_end": **********.912506, "duration": 0.002432107925415039, "duration_str": "2.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.19865, "relative_start": 1.****************, "end": **********.19865, "relative_end": **********.19865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.223783, "relative_start": 1.**************, "end": **********.223783, "relative_end": **********.223783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.237797, "relative_start": 1.1381781101226807, "end": **********.237797, "relative_end": **********.237797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.250871, "relative_start": 1.151252031326294, "end": **********.250871, "relative_end": **********.250871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.257412, "relative_start": 1.1577930450439453, "end": **********.257412, "relative_end": **********.257412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.229984, "relative_start": 3.1303651332855225, "end": **********.229984, "relative_end": **********.229984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.231054, "relative_start": 3.1314351558685303, "end": **********.231054, "relative_end": **********.231054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.237576, "relative_start": 3.1379570960998535, "end": **********.237576, "relative_end": **********.237576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.240339, "relative_start": 3.1407201290130615, "end": **********.240339, "relative_end": **********.240339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.244265, "relative_start": 3.144646167755127, "end": **********.244265, "relative_end": **********.244265, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.249996, "relative_start": 3.150377035140991, "end": **********.251832, "relative_end": **********.251832, "duration": 0.0018360614776611328, "duration_str": "1.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8817536, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.198562, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.223713, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.237704, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.250797, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.257326, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.229861, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.230993, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.237498, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.240188, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.244195, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}]}, "queries": {"count": 501, "nb_statements": 831, "nb_visible_statements": 501, "nb_excluded_statements": 331, "nb_failed_statements": 0, "accumulated_duration": 0.5000800000000001, "accumulated_duration_str": "500ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.925585, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'DRGI5Fzk581uN2bmYZK3WAtdIT8XMC5gAeVQhADN' limit 1", "type": "query", "params": [], "bindings": ["DRGI5Fzk581uN2bmYZK3WAtdIT8XMC5gAeVQhADN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.927527, "duration": 0.015619999999999998, "duration_str": "15.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 3.124}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.971284, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 3.124, "width_percent": 0.438}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.0395288, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 3.561, "width_percent": 0.222}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.049262, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 3.783, "width_percent": 0.166}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-10-07' and date(`semester_end`) >= '2025-10-07' limit 1", "type": "query", "params": [], "bindings": [3, "2025-10-07", "2025-10-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.054378, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 3.949, "width_percent": 0.34}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = '3' and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 191}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.086136, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:191", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=191", "ajax": false, "filename": "OverviewResource.php", "line": "191"}, "connection": "racoed", "explain": null, "start_percent": 4.289, "width_percent": 0.174}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 197}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.093188, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:197", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=197", "ajax": false, "filename": "OverviewResource.php", "line": "197"}, "connection": "racoed", "explain": null, "start_percent": 4.463, "width_percent": 0.422}, {"sql": "select * from `levels` where `levels`.`id` = '2' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 203}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.101799, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:203", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 203}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=203", "ajax": false, "filename": "OverviewResource.php", "line": "203"}, "connection": "racoed", "explain": null, "start_percent": 4.885, "width_percent": 0.194}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 209}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.106888, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:209", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 209}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=209", "ajax": false, "filename": "OverviewResource.php", "line": "209"}, "connection": "racoed", "explain": null, "start_percent": 5.079, "width_percent": 0.312}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16')) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 487}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 465}, {"index": 13, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 163}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.118352, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:487", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=487", "ajax": false, "filename": "OverviewResource.php", "line": "487"}, "connection": "racoed", "explain": null, "start_percent": 5.391, "width_percent": 0.228}, {"sql": "select exists(select * from `scoresheets` where (`school_session_id` = '3' and `semester_id` = '1' and `department_id` = '16' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": ["3", "1", "16", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 499}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 465}, {"index": 13, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 163}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.123337, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:499", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 499}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=499", "ajax": false, "filename": "OverviewResource.php", "line": "499"}, "connection": "racoed", "explain": null, "start_percent": 5.619, "width_percent": 0.184}, {"sql": "select exists(select * from `courses` where (`level_id` = '2' and `semester_id` = '1' and `department_id` = '16') order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC) as `exists`", "type": "query", "params": [], "bindings": ["2", "1", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 476}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 163}, {"index": 13, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 14, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 145}], "start": **********.128264, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:476", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 476}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=476", "ajax": false, "filename": "OverviewResource.php", "line": "476"}, "connection": "racoed", "explain": null, "start_percent": 5.803, "width_percent": 0.642}, {"sql": "select * from `departments` where `departments`.`id` = '16' limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 170}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.135102, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:170", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 170}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=170", "ajax": false, "filename": "OverviewResource.php", "line": "170"}, "connection": "racoed", "explain": null, "start_percent": 6.445, "width_percent": 0.13}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "2", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.141761, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "racoed", "explain": null, "start_percent": 6.575, "width_percent": 0.726}, {"sql": "select * from `users` where `role` = 1 and exists (select * from `applications` where `users`.`id` = `applications`.`user_id` and `admission_status` = 1) and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '16' or `second_department_id` = '16'))))) and `users`.`deleted_at` is null order by `last_name` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 1, "3", "1", "2", "16", "16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.149299, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 7.301, "width_percent": 0.524}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (15, 16, 17, 18, 19, 20, 21, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.155699, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 7.825, "width_percent": 0.246}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 128}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.1838882, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:128", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=128", "ajax": false, "filename": "OverviewResource.php", "line": "128"}, "connection": "racoed", "explain": null, "start_percent": 8.071, "width_percent": 0.266}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 138}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.216731, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:138", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 138}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=138", "ajax": false, "filename": "OverviewResource.php", "line": "138"}, "connection": "racoed", "explain": null, "start_percent": 8.337, "width_percent": 0.144}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 148}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.230921, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:148", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=148", "ajax": false, "filename": "OverviewResource.php", "line": "148"}, "connection": "racoed", "explain": null, "start_percent": 8.481, "width_percent": 0.152}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 158}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.243583, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:158", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 158}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=158", "ajax": false, "filename": "OverviewResource.php", "line": "158"}, "connection": "racoed", "explain": null, "start_percent": 8.633, "width_percent": 0.134}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '2' and `semester_id` = '1' order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": ["16", "2", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}], "start": **********.293865, "duration": 0.00708, "duration_str": "7.08ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 8.767, "width_percent": 1.416}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.3048432, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 10.182, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.309444, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 10.342, "width_percent": 0.312}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 411}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 289}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.3137732, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:411", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 411}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=411", "ajax": false, "filename": "OverviewResource.php", "line": "411"}, "connection": "racoed", "explain": null, "start_percent": 10.654, "width_percent": 0.24}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.3183038, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 10.894, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.321977, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 11.046, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.326345, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 11.23, "width_percent": 0.202}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.330303, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 11.432, "width_percent": 0.172}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.3344262, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 11.604, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.338331, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 11.768, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.342309, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 11.952, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.346105, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 12.11, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.350394, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 12.308, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.35413, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 12.456, "width_percent": 0.174}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.3582668, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 12.63, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.362013, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 12.788, "width_percent": 0.166}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '2' and `semester_id` = '1' order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": ["16", "2", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}], "start": **********.366223, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 12.954, "width_percent": 0.334}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.371628, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 13.288, "width_percent": 0.174}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.37593, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 13.462, "width_percent": 0.206}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.38007, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 13.668, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.3839798, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 13.832, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.387977, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 14.022, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.391808, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 14.178, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.395755, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 14.378, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.399302, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 14.524, "width_percent": 0.18}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.403291, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 14.704, "width_percent": 0.188}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4075942, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 14.892, "width_percent": 0.196}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.411916, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 15.088, "width_percent": 0.188}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.416358, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 15.276, "width_percent": 0.324}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.4212651, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 15.6, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.425427, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 15.771, "width_percent": 0.47}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '2' and `semester_id` = '1' order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": ["16", "2", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 87}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 19, "namespace": "view", "name": "filament-tables::columns.text-column", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/columns/text-column.blade.php", "line": 23}], "start": **********.431679, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.241, "width_percent": 0.29}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.436865, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 16.531, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.440809, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 16.699, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.445239, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 16.889, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4492009, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 17.055, "width_percent": 0.218}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.453168, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 17.273, "width_percent": 0.178}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.457135, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 17.451, "width_percent": 0.388}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.462356, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 17.839, "width_percent": 0.178}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4663951, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 18.017, "width_percent": 0.172}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.470501, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 18.189, "width_percent": 0.178}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.474757, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 18.367, "width_percent": 0.28}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.479271, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 18.647, "width_percent": 0.184}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4834912, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 18.831, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}], "start": **********.487659, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 19.029, "width_percent": 0.15}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 306}, {"index": 24, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.4917362, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 19.179, "width_percent": 0.2}, {"sql": "select * from `grades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 425}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 96}, {"index": 18, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/filament/support/src/Concerns/HasCellState.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\HasCellState.php", "line": 79}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeCopied.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeCopied.php", "line": 18}], "start": **********.496763, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:425", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 425}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=425", "ajax": false, "filename": "OverviewResource.php", "line": "425"}, "connection": "racoed", "explain": null, "start_percent": 19.379, "width_percent": 0.132}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.503401, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 19.511, "width_percent": 0.206}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.5079808, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 19.717, "width_percent": 0.19}, {"sql": "select `max_score` from `grades` where `min_score` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 445}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 327}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.512184, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:445", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 445}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=445", "ajax": false, "filename": "OverviewResource.php", "line": "445"}, "connection": "racoed", "explain": null, "start_percent": 19.907, "width_percent": 0.152}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.5162382, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 20.059, "width_percent": 0.194}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.520421, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 20.253, "width_percent": 0.204}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.52499, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 20.457, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.529478, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 20.623, "width_percent": 0.238}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.5340621, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 20.861, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.53789, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 21.023, "width_percent": 0.17}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.541831, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 21.193, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.545861, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 21.347, "width_percent": 0.192}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.550162, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 21.539, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.554378, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 21.711, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.5588229, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 21.908, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.56314, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 22.088, "width_percent": 0.248}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.567881, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 22.336, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.572052, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 22.504, "width_percent": 0.178}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.5762122, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 22.682, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.581147, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 22.85, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.585196, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 23.032, "width_percent": 0.184}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 345) limit 1", "type": "query", "params": [], "bindings": [20, 345], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.5894861, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 23.216, "width_percent": 0.196}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.595684, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 23.412, "width_percent": 0.178}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 346) limit 1", "type": "query", "params": [], "bindings": [20, 346], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.599793, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 23.59, "width_percent": 0.206}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.604098, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 23.796, "width_percent": 0.174}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 347) limit 1", "type": "query", "params": [], "bindings": [20, 347], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.608891, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 23.97, "width_percent": 0.266}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.613416, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 24.236, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 348) limit 1", "type": "query", "params": [], "bindings": [20, 348], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.617178, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 24.392, "width_percent": 0.168}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.6209972, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 24.56, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 349) limit 1", "type": "query", "params": [], "bindings": [20, 349], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.625213, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 24.722, "width_percent": 0.258}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.630054, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 24.98, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 343) limit 1", "type": "query", "params": [], "bindings": [20, 343], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.633923, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 25.138, "width_percent": 0.168}, {"sql": "select * from `registrations` where (`user_id` = 22 and `school_session_id` = '3' and `semester_id` = '1' and `level_id` = '2') limit 1", "type": "query", "params": [], "bindings": [22, "3", "1", "2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 278}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}], "start": **********.637996, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:439", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=439", "ajax": false, "filename": "OverviewResource.php", "line": "439"}, "connection": "racoed", "explain": null, "start_percent": 25.306, "width_percent": 0.192}, {"sql": "select `total` from `total_scores` where (`registration_id` = 20 and `course_id` = 344) limit 1", "type": "query", "params": [], "bindings": [20, 344], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 325}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 324}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 112}, {"index": 24, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}], "start": **********.642421, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:287", "source": {"index": 17, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 287}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=287", "ajax": false, "filename": "OverviewResource.php", "line": "287"}, "connection": "racoed", "explain": null, "start_percent": 25.498, "width_percent": 0.216}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6467261, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 25.714, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.648428, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 25.876, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.650228, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.04, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.65189, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.194, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6536689, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.358, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.655274, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.516, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.657093, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.692, "width_percent": 0.17}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.658895, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.862, "width_percent": 0.148}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.660747, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.01, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.662529, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.178, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.665364, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.378, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6671839, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.558, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.669135, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.74, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.67083, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.9, "width_percent": 0.166}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.672651, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.066, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.674476, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.231, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.676404, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.413, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6781409, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.573, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.6800218, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.749, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.681729, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.907, "width_percent": 0.194}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.683751, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.101, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.685471, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.263, "width_percent": 0.17}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.687352, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.433, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.688983, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.575, "width_percent": 0.156}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.733745, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.731, "width_percent": 0.372}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.736944, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.103, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.738496, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.251, "width_percent": 0.136}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.74006, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.387, "width_percent": 0.13}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.741546, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.517, "width_percent": 0.148}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7432492, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.665, "width_percent": 0.124}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.744704, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.789, "width_percent": 0.168}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7464821, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.957, "width_percent": 0.124}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7478878, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.081, "width_percent": 0.156}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7495139, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.237, "width_percent": 0.122}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750957, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.359, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75292, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.547, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7546551, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.701, "width_percent": 0.208}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.756788, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.909, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.75845, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.063, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.760432, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.237, "width_percent": 0.288}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.762944, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.525, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764487, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.663, "width_percent": 0.16}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7663429, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.823, "width_percent": 0.176}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7681532, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.999, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7699559, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.163, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.771528, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.305, "width_percent": 0.18}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.773491, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.485, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.775063, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.633, "width_percent": 0.17}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.776896, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.803, "width_percent": 0.144}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.778584, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.947, "width_percent": 0.17}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.780461, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.117, "width_percent": 0.174}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7822812, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.291, "width_percent": 0.168}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7840369, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.458, "width_percent": 0.15}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.785667, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.608, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.788793, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.798, "width_percent": 0.282}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.791821, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.08, "width_percent": 0.184}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.793874, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.264, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.795906, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.452, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.797661, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.62, "width_percent": 0.202}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.799716, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.822, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.801468, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.978, "width_percent": 0.186}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.803465, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.164, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.805321, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.344, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8078349, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.526, "width_percent": 0.222}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.809899, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.748, "width_percent": 0.208}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8120239, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.956, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8138459, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.104, "width_percent": 0.214}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.81613, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.318, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.817855, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.49, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.823075, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.672, "width_percent": 0.17}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.824874, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.842, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.82722, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.03, "width_percent": 0.192}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.82953, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.222, "width_percent": 0.208}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.831711, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.43, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.833532, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.596, "width_percent": 0.212}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.835688, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.808, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.837399, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.968, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.839233, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.144, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.841095, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.292, "width_percent": 0.278}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84355, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.57, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8454318, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.736, "width_percent": 0.224}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84777, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.96, "width_percent": 0.174}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.84984, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.134, "width_percent": 0.216}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.852317, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.35, "width_percent": 0.198}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.854309, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.548, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.85643, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.723, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8583539, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.889, "width_percent": 0.292}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.860889, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.181, "width_percent": 0.182}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8627179, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.363, "width_percent": 0.218}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.864832, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.581, "width_percent": 0.17}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.866616, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.751, "width_percent": 0.174}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.868695, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.925, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.87047, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.089, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8724601, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.265, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8743649, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.433, "width_percent": 0.224}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.876481, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.657, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8783479, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.837, "width_percent": 0.224}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8811579, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.061, "width_percent": 0.498}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8847568, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.559, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8869162, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.747, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.888661, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.909, "width_percent": 0.186}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.890728, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.095, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8924909, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.259, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8945339, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.447, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.896242, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.605, "width_percent": 0.174}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898159, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.779, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8998818, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.931, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.901891, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.115, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903644, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.269, "width_percent": 0.178}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9056609, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.447, "width_percent": 0.178}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9075658, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.625, "width_percent": 0.22}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9106321, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.845, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912334, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.009, "width_percent": 0.192}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.914325, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.201, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.915987, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.353, "width_percent": 0.16}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9178822, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.513, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.919684, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.673, "width_percent": 0.174}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9216762, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.847, "width_percent": 0.202}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923665, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.048, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9256248, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.232, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927337, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.386, "width_percent": 0.18}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929235, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.566, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930784, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.714, "width_percent": 0.138}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932338, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.852, "width_percent": 0.124}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9338262, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.976, "width_percent": 0.16}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947138, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.136, "width_percent": 0.302}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9499528, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.438, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951878, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.596, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953936, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.794, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955649, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.954, "width_percent": 0.178}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957774, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.132, "width_percent": 0.206}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959716, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.338, "width_percent": 0.172}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961672, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.51, "width_percent": 0.174}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9635382, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.684, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965637, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.884, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967258, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.038, "width_percent": 0.154}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969118, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.192, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9709709, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.358, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973191, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.546, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9749131, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.71, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.976957, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.878, "width_percent": 0.32}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979919, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.198, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9816232, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.354, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98368, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.554, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9854958, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.72, "width_percent": 0.228}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9877868, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.948, "width_percent": 0.508}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.991165, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 52.456, "width_percent": 0.188}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99307, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 52.644, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9948258, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 52.8, "width_percent": 0.252}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.997318, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.052, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.999476, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.231, "width_percent": 0.18}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.001446, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.411, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0031428, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.563, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.005008, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.747, "width_percent": 0.2}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.007118, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.947, "width_percent": 0.31}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.010762, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.257, "width_percent": 0.342}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.013635, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.599, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0151842, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.745, "width_percent": 0.204}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.017115, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.949, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.018586, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.087, "width_percent": 0.158}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.020276, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.245, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.021851, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.391, "width_percent": 0.194}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0237072, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.585, "width_percent": 0.204}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.025595, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.789, "width_percent": 0.208}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.027537, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.997, "width_percent": 0.134}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.029099, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.131, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.030967, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.315, "width_percent": 0.13}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.032493, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.445, "width_percent": 0.206}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.034577, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.651, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.036248, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.807, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0408719, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.983, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.042686, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.135, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0448928, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.333, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.046722, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.491, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.048781, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.673, "width_percent": 0.188}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.050689, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.861, "width_percent": 0.21}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.052738, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.071, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0543709, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.213, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.056438, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.395, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.058084, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.543, "width_percent": 0.18}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.060092, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.723, "width_percent": 0.17}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.061853, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.893, "width_percent": 0.174}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.063781, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.067, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0654101, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.213, "width_percent": 0.204}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.067565, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.416, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0692592, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.57, "width_percent": 0.23}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0715919, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.8, "width_percent": 0.18}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0733738, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.98, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.075277, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.17, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0769138, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.332, "width_percent": 0.192}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.078915, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.524, "width_percent": 0.17}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.080696, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.694, "width_percent": 0.408}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0837061, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.102, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.085554, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.27, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0874188, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.454, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.089109, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.612, "width_percent": 0.206}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.09113, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.818, "width_percent": 0.202}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0931349, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.02, "width_percent": 0.288}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.096122, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.308, "width_percent": 0.184}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.0979528, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.492, "width_percent": 0.186}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.100079, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.678, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.101883, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.85, "width_percent": 0.296}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1049738, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.146, "width_percent": 0.224}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.107111, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.37, "width_percent": 0.21}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.109617, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.58, "width_percent": 0.208}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.111579, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.788, "width_percent": 0.208}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.11355, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.996, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.115229, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.162, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.11719, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.352, "width_percent": 0.186}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1190422, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.538, "width_percent": 0.194}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.12114, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.732, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.123281, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.898, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.128749, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.088, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.130442, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.246, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.132309, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.422, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.134019, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.57, "width_percent": 0.192}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.136035, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.761, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.137774, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.925, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.139961, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.123, "width_percent": 0.178}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.141748, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.301, "width_percent": 0.206}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.143661, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.507, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.145098, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.645, "width_percent": 0.13}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.146725, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.775, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.148376, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.929, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.150527, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.119, "width_percent": 0.204}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1523669, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.323, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.166107, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.513, "width_percent": 0.318}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.168904, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.831, "width_percent": 0.176}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1707852, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.007, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.172812, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.171, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.174751, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.343, "width_percent": 0.218}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1768649, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.561, "width_percent": 0.184}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.178713, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.745, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1807601, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.945, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.182468, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.109, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.184351, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.293, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1859171, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.435, "width_percent": 0.196}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.187911, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.631, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.189662, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.783, "width_percent": 0.162}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.191514, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.945, "width_percent": 0.202}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.1932929, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.147, "width_percent": 0.15}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.195047, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.297, "width_percent": 0.226}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.197222, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.523, "width_percent": 0.126}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.198679, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.649, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2004468, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.813, "width_percent": 0.12}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.201888, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.933, "width_percent": 0.138}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.203558, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.071, "width_percent": 0.128}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.205168, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.199, "width_percent": 0.152}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.206921, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.351, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.208602, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.511, "width_percent": 0.172}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.210467, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.683, "width_percent": 0.126}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.211869, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.809, "width_percent": 0.136}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.21346, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.944, "width_percent": 0.144}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2150998, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.088, "width_percent": 0.152}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.216894, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.24, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2184591, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.382, "width_percent": 0.13}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.221009, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.512, "width_percent": 0.302}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2236512, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.814, "width_percent": 0.162}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.225365, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.976, "width_percent": 0.182}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.227217, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.158, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.22902, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.316, "width_percent": 0.204}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.231171, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.52, "width_percent": 0.19}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2330592, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.71, "width_percent": 0.234}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.235635, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.944, "width_percent": 0.288}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.238692, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.232, "width_percent": 0.268}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2415428, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.5, "width_percent": 0.576}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.245366, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.076, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.247287, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.274, "width_percent": 0.15}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.249026, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.424, "width_percent": 0.168}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2509089, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.592, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.252619, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.746, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2572298, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.93, "width_percent": 0.226}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.26013, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.156, "width_percent": 0.394}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.263124, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.55, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.264948, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.708, "width_percent": 0.176}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.266875, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.884, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2686431, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.044, "width_percent": 0.264}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.271096, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.308, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2729352, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.46, "width_percent": 0.17}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.275244, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.63, "width_percent": 0.296}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.277744, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.926, "width_percent": 0.166}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.279516, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.092, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.280956, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.233, "width_percent": 0.136}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.282493, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.369, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.284021, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.507, "width_percent": 0.154}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.285889, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.661, "width_percent": 0.144}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2873602, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.805, "width_percent": 0.126}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.288938, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.931, "width_percent": 0.128}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.29047, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.059, "width_percent": 0.154}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.2922342, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.213, "width_percent": 0.126}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.293661, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.339, "width_percent": 0.124}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.295198, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.463, "width_percent": 0.102}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.296496, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.565, "width_percent": 0.114}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.297925, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.679, "width_percent": 0.12}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.299187, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.799, "width_percent": 0.102}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3005269, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.901, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3017938, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.011, "width_percent": 0.122}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3032289, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.133, "width_percent": 0.11}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.304564, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.243, "width_percent": 0.216}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3069718, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.459, "width_percent": 0.228}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.308892, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.687, "width_percent": 0.146}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3105059, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.833, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.312223, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.987, "width_percent": 0.22}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3144119, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.207, "width_percent": 0.216}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.316491, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.423, "width_percent": 0.212}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.318826, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.635, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.320632, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.803, "width_percent": 0.158}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.323133, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.961, "width_percent": 0.182}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3254602, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.143, "width_percent": 0.37}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3283439, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.513, "width_percent": 0.19}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.330097, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.703, "width_percent": 0.142}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.33234, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.845, "width_percent": 0.29}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.335042, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.135, "width_percent": 0.34}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3393679, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.475, "width_percent": 0.192}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.341628, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.667, "width_percent": 0.34}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.344891, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.007, "width_percent": 0.268}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.347911, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.275, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.349885, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.458, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.35145, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.596, "width_percent": 0.144}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.353138, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.74, "width_percent": 0.13}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3546422, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.87, "width_percent": 0.16}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.356625, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.03, "width_percent": 0.138}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.359103, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.168, "width_percent": 0.24}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3614619, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.408, "width_percent": 0.182}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.364124, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.59, "width_percent": 0.17}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.366012, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.76, "width_percent": 0.132}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.367521, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.892, "width_percent": 0.172}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.382837, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.064, "width_percent": 0.304}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.385613, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.368, "width_percent": 0.148}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3872378, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.516, "width_percent": 0.19}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.389206, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.706, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3911, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.864, "width_percent": 0.344}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.394039, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.208, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.395785, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.38, "width_percent": 0.214}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.3979921, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.594, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.399899, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.766, "width_percent": 0.18}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.401889, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.946, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.403643, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.114, "width_percent": 0.186}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4056542, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.3, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.407545, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.46, "width_percent": 0.244}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4098601, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.704, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.411661, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.876, "width_percent": 0.182}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.413793, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.058, "width_percent": 0.334}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.416822, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.392, "width_percent": 0.158}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.418626, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.55, "width_percent": 0.174}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.420856, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.724, "width_percent": 0.152}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4226122, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.876, "width_percent": 0.162}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.424642, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.038, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.42642, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.206, "width_percent": 0.172}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.428302, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.378, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4300442, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.542, "width_percent": 0.202}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.432141, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.743, "width_percent": 0.142}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.433827, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.885, "width_percent": 0.218}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.435995, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.103, "width_percent": 0.17}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.437771, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.273, "width_percent": 0.204}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4398532, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.477, "width_percent": 0.164}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.441591, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.641, "width_percent": 0.178}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by CAST(REGEXP_REPLACE(code, \"[^0-9]\", \"\") AS UNSIGNED) ASC", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.444326, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.819, "width_percent": 0.33}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4474468, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.149, "width_percent": 0.294}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.449949, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.443, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.452152, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.643, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.454029, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.811, "width_percent": 0.196}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.456121, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.007, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.457985, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.175, "width_percent": 0.356}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.461569, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.531, "width_percent": 0.176}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.463509, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.707, "width_percent": 0.214}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.465762, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.921, "width_percent": 0.184}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4677331, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.105, "width_percent": 0.234}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4701009, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.339, "width_percent": 0.186}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.472122, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.525, "width_percent": 0.208}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.474544, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.733, "width_percent": 0.304}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.477062, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.037, "width_percent": 0.2}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.482032, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.237, "width_percent": 0.172}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.483848, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.409, "width_percent": 0.198}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.4859369, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.607, "width_percent": 0.146}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.487599, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.753, "width_percent": 0.246}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.489911, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.999, "width_percent": 0.144}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.491644, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.143, "width_percent": 0.184}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.49364, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.327, "width_percent": 0.168}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.49539, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.495, "width_percent": 0.158}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.497124, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.653, "width_percent": 0.13}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.49867, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.783, "width_percent": 0.178}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.500597, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.96, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.502277, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.12, "width_percent": 0.142}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.503957, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.262, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5057018, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.422, "width_percent": 0.192}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5080142, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.614, "width_percent": 0.23}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.510082, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.844, "width_percent": 0.156}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.511942, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98, "width_percent": 0.154}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.513703, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.154, "width_percent": 0.196}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5158231, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.35, "width_percent": 0.166}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.517645, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.516, "width_percent": 0.156}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.519523, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.672, "width_percent": 0.156}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.521197, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.828, "width_percent": 0.144}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5230858, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.972, "width_percent": 0.15}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.524803, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.122, "width_percent": 0.212}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5270329, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.334, "width_percent": 0.15}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5287871, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.484, "width_percent": 0.164}, {"sql": "select * from `registrations` where (`user_id` = ? and `school_session_id` = ? and `semester_id` = ? and `level_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.530725, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.648, "width_percent": 0.16}, {"sql": "select `total` from `total_scores` where (`registration_id` = ? and `course_id` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1759824773.5324578, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.808, "width_percent": 0.192}, {"sql": "... 331 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"App\\Models\\Registration": {"value": 424, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\TotalScore": {"value": 392, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FTotalScore.php&line=1", "ajax": false, "filename": "TotalScore.php", "line": "?"}}, "App\\Models\\Course": {"value": 168, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Department": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Grade": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FGrade.php&line=1", "ajax": false, "filename": "Grade.php", "line": "?"}}, "App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\Semester": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 1032, "is_counter": true}, "livewire": {"data": {"app.filament.staff.resources.overview-resource.pages.manage-overview #ZWaDbbkncfFm9bsvLyDv": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"2\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"2\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.resources.overview-resource.pages.manage-overview\"\n  \"component\" => \"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\"\n  \"id\" => \"ZWaDbbkncfFm9bsvLyDv\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview@loadTable<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/CanDeferLoading.php:17-20</a>", "middleware": "web", "duration": "3.2s", "peak_memory": "14MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1788133978 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1788133978\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-203695128 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3RXMxWAAaP9JkLBjitNoCaCBHmS5J9WjQ8fBFne0</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1883 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;2&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;2&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;ZWaDbbkncfFm9bsvLyDv&quot;,&quot;name&quot;:&quot;app.filament.staff.resources.overview-resource.pages.manage-overview&quot;,&quot;path&quot;:&quot;staff\\/overviews&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;538addebff5b17228628491dade196e08c3a8d84a2e3cd48341da68ac5dca4a4&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">loadTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203695128\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1218234995 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1254 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImtVU3dpWnFDRFNWM1d3RC94eEY5QXc9PSIsInZhbHVlIjoiZjhCa00yYitZWWdmZVBEaWNPelEyVTR6MzFUTlhQZ3VacGFDNS9TOUU0QTJxbzJvSmtuRnB5WmYvTk5HS0RtZDF3K1VtOTYrL0RGZENaRkJpT2lScWkyT25lSGxaMFNWMzJIQmtwWDB1K0hkSlZ4NitjckZybEZrbnpZSG9DMHAxTlo1eno2VVZXenJGdWVDZDVuUlhYUG05WU9QVEV2UkxTbU1YWllGc0czWTB3MGt1QW9Ha0tCMG8xZXRHbGZCVUEvOHdwMzlKYjhkNXV2THFsdnVXWjF0K25JN0dXdnF4Ym5JQkJZY1ZUQT0iLCJtYWMiOiJlOGIwOWFiY2ZkNjRiNTM3YThlY2RlMjU3MzA0NWFlODdiZGZhY2Q5MzBmYzE2OWZhNjdlMGU5MjhhMjdkMjI5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlQ1RDdHMWJKQkZpTGp4WGEyQnBDYVE9PSIsInZhbHVlIjoiOFZadFZCajlKZmptK3BLbDFaLzNQcVVnL2pZZm84S29aY0xOMXdwa044SFdmbzVlZkE4M3JrN3RmTFIwVHFZSElNUmJnb2I0blNWRjdYN24reEF0cllIeFNmcGdWdW93V1VrNVJ0b1hmRS8xdjVsSWFNUEhEVDRSbVVFbnJ1NmsiLCJtYWMiOiI5N2MwNDQxNWYzZTM1NGMxYzZlM2FhM2UyMDBjYzgwY2E0NWRmYTdhZDAzNjA4YWM0NzRkZmNlNmM0YzEzZTBmIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IkJjSUlvRUtJbzk3cE9NdEdIOE5YWVE9PSIsInZhbHVlIjoiQzJISVZTSmcrWlpmKzR2RzFsbmlhZ01neEtuOWc3NzBsNU1RRVhmZ091QzRuTnlvVXYzdmNNMWZybFJvS1pyZnl6bzhmZFd4YUxpa3NLZFA2aWxNdFQwc3d6NGU1UlB3bW9uMnNEakZmWlJXSUd2WXpZSGdncXJCaktsOUNoODciLCJtYWMiOiJkMzcxMjBhZGUyN2M5N2Q3OWFlODU3OTM0ZDAzN2JmZWY5NGRiZTkwZjc0ODJkODdmOTAwMmE5MDU5ZGViMTE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"228 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=2&amp;tableFilters[overview_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Google Chrome&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2276</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218234995\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2000954561 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|1H5vZ3kUjepNqOWpqLfZbHhO0p3Hpb1rxDVxB6uHj7v9NbxaMJIfpkgPgReu|$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3RXMxWAAaP9JkLBjitNoCaCBHmS5J9WjQ8fBFne0</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">DRGI5Fzk581uN2bmYZK3WAtdIT8XMC5gAeVQhADN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000954561\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-214810783 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 07 Oct 2025 08:12:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214810783\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1557367435 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">3RXMxWAAaP9JkLBjitNoCaCBHmS5J9WjQ8fBFne0</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"260 characters\">https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=3&amp;tableFilters%5Boverview_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Blevel_id%5D=2&amp;tableFilters%5Boverview_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557367435\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}
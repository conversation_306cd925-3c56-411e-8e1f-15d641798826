@php

use App\Filament\Staff\Clusters\Scores\Resources\ScoreResource;


$hasAllFilters = ScoreResource::hasRequiredFilters($this);

$scoresheetPublished = $hasAllFilters ? ScoreResource::isScoresheetPublished($this) : false;

$scoresheetCreated = $hasAllFilters ? ScoreResource::isScoresheetCreated($this) : false;

@endphp

@if($hasAllFilters)
    <div class="max-w-md mx-auto px-2">
        @if ($scoresheetPublished)
            <div class="bg-green-100 border border-green-400 text-green-800 px-4 py-3 rounded-sm flex items-center justify-center gap-2 text-sm font-semibold">
                <x-filament::icon icon="heroicon-s-check-circle" class="w-5 h-5 text-green-600" />
                Scoresheet has been published.
                @if (main_staff_access())
                    Scores upload is not allowed from departments.
                @else
                    Scores upload is not allowed. Please contact the school admin for help.
                @endif
            </div>
        @elseif ($scoresheetCreated)
            <div class="bg-amber-100 border border-amber-400 text-amber-800 px-4 py-3 rounded-sm flex items-center justify-center gap-2 text-sm font-semibold">
                <x-filament::icon icon="heroicon-s-exclamation-circle" class="w-5 h-5 text-amber-600" />
                Scoresheet has been created. Scores upload is allowed.
            </div>
        @else
            <div class="bg-red-100 border border-red-400 text-red-800 px-4 py-3 rounded-sm flex items-center justify-center gap-2 text-sm font-semibold">
                <x-filament::icon icon="heroicon-s-x-circle" class="w-5 h-5 text-red-600" />
                Scoresheet has not been created for this options.
            </div>
        @endif
    </div>
@endif





<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Department extends Model
{
    // cast
    protected $casts = [
        'is_edu' => 'boolean',
        'is_gse' => 'boolean',
    ];

    public function name(): Attribute
    {
        return Attribute::get(fn ($value) => Str::title($value));
    }

    /**
     * Get the school that owns the Department
     */
    public function school(): BelongsTo
    {
        return $this->belongsTo(School::class);
    }

    /**
     * Get the head of departments that belong to the Department
     */
    public function headOfDepartments(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * Get the users that belong to the Department
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class);
    }
}

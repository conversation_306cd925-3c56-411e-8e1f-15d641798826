<?php

namespace App\Filament\Staff\Resources\StaffResource\Pages;

use App\Enums\Role;
use App\Filament\Staff\Resources\StaffResource;
use App\Models\Department;
use App\Models\User;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateStaff extends CreateRecord
{
    protected static string $resource = StaffResource::class;

    protected function beforeCreate(): void
    {
        $restrictedRoles = array_map(fn ($role) => $role->value, management_staff_roles());
        $role = (int) $this->data['role'];

        if ($role === Role::HOD->value) {

            $departmentHasHod = Department::whereIn('id', $this->data['departments'] ?? [])
                ->whereHas('headOfDepartments')
                ->exists();

            if ($departmentHasHod) {
                Notification::make()
                    ->title('Creation Failed')
                    ->body('An HOD already exists for this department. Please remove the existing one before creating a new one.')
                    ->danger()
                    ->send();

                $this->halt();
            }
        } elseif (in_array($role, $restrictedRoles)) {
            if (User::where('role', $role)->exists()) {
                Notification::make()
                    ->title('Creation Failed')
                    ->body('A staff with this role already exists for this college. Please delete the existing one before creating a new one.')
                    ->danger()
                    ->send();

                $this->halt();
            }
        }
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Staff Created')
            ->body('The staff has been registered successfully.');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}

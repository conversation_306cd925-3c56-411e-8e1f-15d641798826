<?php

namespace App\Providers\Filament;

use Filament\Panel;
use Illuminate\View\View;
use Filament\PanelProvider;
use Filament\Navigation\MenuItem;
use Illuminate\Support\HtmlString;
use Filament\View\PanelsRenderHook;
use Filament\Support\Enums\MaxWidth;
use Filament\Http\Middleware\Authenticate;
use Illuminate\Session\Middleware\StartSession;
use Devonab\FilamentEasyFooter\EasyFooterPlugin;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Filament\Http\Middleware\AuthenticateSession;
use App\Filament\Student\Pages\Auth\ChangePassword;
use App\Filament\Student\Pages\Auth\StudentProfile;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;

class StudentPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->domain(config('custom.portal_url'))
            ->spa()
            ->id('student')
            ->path('student')
            // ->emailVerification()
            ->profile(StudentProfile::class)
            ->sidebarCollapsibleOnDesktop()
            ->sidebarWidth('15rem')
            ->databaseNotifications()
            ->databaseTransactions()
            ->simplePageMaxContentWidth(MaxWidth::FourExtraLarge)

            ->userMenuItems([
                'profile' => MenuItem::make()->label('Bio-data'),
                'logout' => MenuItem::make()->label('Log out'),
                MenuItem::make()
                    ->label('Change password')
                    ->url(fn(): string => ChangePassword::getUrl(['panel' => 'student']))
                    ->icon('heroicon-o-key')
            ])

            ->renderHook(
                PanelsRenderHook::PAGE_START,
                fn(): View => view('filament.hooks.global-bio-data-banner')
            )
            ->renderHook(
                PanelsRenderHook::FOOTER,
                fn(): string => new HtmlString('<span class= "text-center text-gray-500 text-[10px]">Thank you for using <a href="https://www.cportal.ng" target="_blank" rel="noopener noreferrer" class="font-semibold">Cportal</a></span>')
            )

            ->darkMode(false)
            ->brandLogo(asset('images/cportal-logo.png'))
            ->darkModeBrandLogo(asset('images/cportal-logo-light.png'))
            ->brandLogoHeight('2rem')
            ->favicon(asset('images/racoed-favicon.png'))
            ->viteTheme('resources/css/filament/custom/theme.css')

            ->discoverClusters(in: app_path('Filament/Student/Clusters'), for: 'App\\Filament\\Student\\Clusters')
            ->discoverPages(in: app_path('Filament/Student/Pages'), for: 'App\\Filament\\Student\\Pages')
            ->discoverResources(in: app_path('Filament/Student/Resources'), for: 'App\\Filament\\Student\\Resources')
            ->discoverWidgets(in: app_path('Filament/Student/Widgets'), for: 'App\\Filament\\Student\\Widgets')

            ->plugins([
                EasyFooterPlugin::make()
                    ->withFooterPosition('footer')
                    ->withBorder()
                    ->hiddenFromPagesEnabled()
                    ->withSentence('RACOED')
                    ->hiddenFromPages(['login', 'forgot-password', 'register', 'reset-password'])
                    ->withGithub(showLogo: false, showUrl: false),
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}

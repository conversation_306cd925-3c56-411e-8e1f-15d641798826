<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum OLevelGrade: string implements HasLabel

{
    case A1 = 'a1';
    case A2 = 'a2';
    case A3 = 'a3';
    case B2 = 'b2';
    case B3 = 'b3';
    case C4 = 'c4';
    case C5 = 'c5';
    case C6 = 'c6';
    case D7 = 'd7';
    case E8 = 'e8';
    case F9 = 'f9';
    case P7 = 'p7';
    case P8 = 'p8';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::A1 => 'A1',
            self::A2 => 'A2',
            self::A3 => 'A3',
            self::B2 => 'B2',
            self::B3 => 'B3',
            self::C4 => 'C4',
            self::C5 => 'C5',
            self::C6 => 'C6',
            self::D7 => 'D7',
            self::E8 => 'E8',
            self::F9 => 'F9',
            self::P7 => 'P7',
            self::P8 => 'P8',
        };
    }
}

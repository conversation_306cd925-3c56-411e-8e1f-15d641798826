<?php

namespace App\Filament\Staff\Resources\StudentResource\Pages;

use App\Enums\AdmissionStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Staff\Resources\StudentResource;

class EditStudent extends EditRecord
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\ViewAction::make(),
            // Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['guardian']['title'] = $this->record->guardian?->title ?? null;
        $data['guardian']['relationship'] = $this->record->guardian?->relationship ?? null;
        $data['guardian']['occupation'] = $this->record->guardian?->occupation ?? null;
        $data['guardian']['phone'] = $this->record->guardian?->phone ?? null;
        $data['guardian']['first_name'] = $this->record->guardian?->first_name ?? null;
        $data['guardian']['last_name'] = $this->record->guardian?->last_name ?? null;
        $data['application']['secondary_school_attended'] = $this->record->application?->secondary_school_attended ?? null;
        $data['application']['secondary_school_graduation_year'] = $this->record->application?->secondary_school_graduation_year ?? null;
        $data['application']['jamb_registration_number'] = $this->record->application?->jamb_registration_number ?? null;
        $data['application']['jamb_score'] = $this->record->application?->jamb_score ?? null;
        $data['application']['exam_board'] = $this->record->application?->exam_board ?? null;
        $data['application']['programme_id'] = $this->record->application?->programme?->id ?? null;
        $data['application']['school_session_id'] = $this->record->application?->schoolSession?->id ?? null;
        $data['application']['exam_result'] =  $this->record->application?->exam_result ?? [];
        $data['activeSchoolSession'] = $this->record->activeRegistration?->schoolSession?->name ?? null;
        $data['activeSemester'] = $this->record->activeRegistration?->semester?->name ?? null;
        $data['activeLevel'] = $this->record->activeRegistration?->level?->name ?? null;
        $data['activeProgramme'] = $this->record->activeRegistration?->programme?->name ?? null;

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        DB::transaction(function () use ($record, $data) {
            $userData = collect($data)->except(['application', 'registrations', 'guardian', 'activeRegistration'])->toArray();
            $record->update($userData);

            if (isset($data['guardian'])) {
                $record->guardian()->updateOrCreate([], $data['guardian']);
            }

            if (isset($data['application'])) {
                $record->application()->updateOrCreate([], $data['application']);
            }

            if (isset($data['registrations'])) {
                $record->registrations()->updateOrCreate([], $data['registrations']);
            }
        });

        return $record->fresh();
    }


    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Student Updated')
            ->body("The student {$this->record->name} has been updated successfully.");
    }

    protected function getRedirectUrl(): string
    {
        $record = $this->getRecord();

        $activeTab = match ($record->application->admission_status) {
            AdmissionStatus::PENDING => 'pending',
            AdmissionStatus::APPROVED => 'registered',
            default => 'registered',
        };

        return $this->getResource()::getUrl('index', [
            'activeTab' => $activeTab,
        ]);
    }
}

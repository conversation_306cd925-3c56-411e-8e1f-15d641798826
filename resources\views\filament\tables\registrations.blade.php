@php
use App\Enums\Role;

if (isset($getRecord) && $student = $getRecord()) {
    if ($student->role === Role::STUDENT) {
        $registrations = $student->registrations
            ->sortBy('created_at')
            ->values();
    }
}

@endphp

<div>
<table class="table-auto border w-full text-left my-4">
    <thead>
        <tr class="bg-gray-100">
            <th class="border px-4 py-2 text-center" colspan="6">Course Registrations</th>
        </tr>
        <tr class="bg-gray-100 text-sm">           
            <th class="border px-4 py-2">Session</th>
            <th class="border px-4 py-2">Semester</th>
            <th class="border px-4 py-2">Level</th>
            <th class="border px-4 py-2">Programme</th>
            <th class="border px-4 py-2">Status</th>
            <th class="border px-4 py-2">Action</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($registrations as $registration)
            <tr class="text-sm">
                <td class="border px-4 py-2">{{ $registration->schoolSession->name }}</td>
                <td class="border px-4 py-2">{{ $registration->semester->name }}</td>
                <td class="border px-4 py-2">{{ $registration->level->name }}</td>
                <td class="border px-4 py-2">{{ $registration->programme->name }}</td>                
                <td class="border px-4 py-2">
                    <livewire:registration-toggle :registration="$registration" :key="$registration->id" />
                </td>
                <td class="border px-4 py-2">
                    <x-filament::dropdown placement="bottom-start">
                        <x-slot name="trigger">                            
                            <x-filament::icon-button icon="heroicon-m-document-text" color="primary" x-tooltip.raw="Export registration"/>
                        </x-slot>
                        <x-filament::dropdown.list>
                           
                        {{-- NOTE: Pause on this limitation for now till second semester 2025 --}}
                           {{-- @if ($student->role === Role::STUDENT && $registration->portalInvoice?->invoice_status !== InvoiceStatus::PAID)
                                <p class="text-red-500 pl-2">No portal fee</p>
                            @else --}}
                                <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printRegistration({{ $registration->id }})">
                                    Print
                                </x-filament::dropdown.list.item>
                                <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadRegistration({{ $registration->id }})">
                                    Download
                                </x-filament::dropdown.list.item>
                            {{-- @endif --}}
                        </x-filament::dropdown.list>
                    </x-filament::dropdown>               
                </td>
            </tr>
        @endforeach
    </tbody>
</table>
</div>

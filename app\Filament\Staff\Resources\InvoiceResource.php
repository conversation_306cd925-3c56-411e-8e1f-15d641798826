<?php

namespace App\Filament\Staff\Resources;

use App\Enums\FeeType;
use App\Models\Fee;
use Filament\Tables;
use App\Models\Level;
use App\Models\Invoice;
use App\Models\Semester;
use Filament\Tables\Table;
use App\Enums\InvoiceStatus;
use App\Models\Registration;
use App\Models\SchoolSession;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\URL;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Staff\Resources\InvoiceResource\Pages;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationGroup = 'CPay™';

    public static function canAccess(): bool
    {
        return management_staff_access();
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('invoice_status', InvoiceStatus::PAID)
            ->whereHasMorph('payable', [Fee::class, Registration::class])
            ->with(['user', 'fee', 'payable', 'payable.level', 'payable.semester', 'payable.schoolSession']);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()
            ->count();
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->paginated([5, 10, 25])
            ->striped()
            ->recordAction(null)
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading('No Invoices Yet')
            ->emptyStateDescription('Once a payment is made, it will appear here.')
            ->description('List of all invoices generated for students’ payments and fees.')
            ->searchPlaceholder('Search (invoice no.)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('number')
                    ->searchable()
                    ->label('Invoice no.')
                    ->description(fn ($record) => $record->user->name),
                TextColumn::make('total_amount')
                    ->prefix('₦')
                    ->formatStateUsing(fn($state) => number_format($state)),
                TextColumn::make('description')
                    ->listWithLineBreaks()
                    ->bulleted()
                    ->getStateUsing(
                        fn($record) => is_array($record->fee?->description ?? $record->description)
                            ? array_map(
                                fn($item) => $item['item'] . ' - ₦' . number_format($item['amount']),
                                $record->fee?->description ?? $record->description
                            )
                            : [$record->fee?->description ?? $record->description]
                    ),
                TextColumn::make('period')
                    ->listWithLineBreaks()
                    ->bulleted(),
                TextColumn::make('fee_type')
                    ->label('Fee type')
                    ->badge(),
                TextColumn::make('invoice_status')
                    ->badge()
                    ->label('Status'),
                TextColumn::make('paid_at')
                    ->label('Date')
                    ->date()
                    ->sinceTooltip()
                    ->sortable(),
            ])

            ->filters([
                SelectFilter::make('school_session_id')
                    ->label('Session')
                    ->options(function () {
                        return SchoolSession::query()
                            ->orderBy('name', 'desc')
                            ->pluck('name', 'id');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query, $value): Builder => $query->whereHasMorph(
                                'payable',
                                [Registration::class],
                                fn(Builder $query) => $query->where('school_session_id', $value)
                            )
                        );
                    })
                    ->native(false)
                    ->default(activeSchoolSession()->id),
                SelectFilter::make('semester_id')
                    ->label('Semester')
                    ->options(function () {
                        return Semester::query()
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query, $value): Builder => $query->whereHasMorph(
                                'payable',
                                [Registration::class],
                                fn(Builder $query) => $query->where('semester_id', $value)
                            )
                        );
                    })
                    ->native(false)
                    ->default(activeSemester()?->id),
                SelectFilter::make('level_id')
                    ->label('Level')
                    ->options(function () {
                        return Level::query()
                            ->orderBy('name')
                            ->pluck('name', 'id');
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn(Builder $query, $value): Builder => $query->whereHasMorph(
                                'payable',
                                [Registration::class],
                                fn(Builder $query) => $query->where('level_id', $value)
                            )
                        );
                    })
                    ->native(false),
                SelectFilter::make('fee_type')
                    ->label('Fee type')
                    ->options(FeeType::class)
                    ->native(false),

            ], layout: FiltersLayout::AboveContent)
            ->filtersApplyAction(
                fn(Action $action) => $action->label('View invoices'),
            )

            ->actions([
                ActionGroup::make([
                    Tables\Actions\Action::make('print')
                        ->disabled(fn($record) => $record->invoice_status !== InvoiceStatus::PAID || !main_staff_plus_bursar_access())
                        ->icon('heroicon-m-printer')
                        ->action(function ($record, $livewire) {

                            // Generate signed URL
                            $url = URL::signedRoute('invoice.print', ['invoice' => $record->id]);

                            $livewire->js("(function() {
                                    const newWindow = window.open(
                                        '$url',
                                        'Invoice',
                                        'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                                    );

                                    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                                        alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                                    } else {
                                        newWindow.focus();
                                    }
                                })();");
                        }),
                    Tables\Actions\Action::make('download')
                        ->disabled(fn($record) => $record->invoice_status !== InvoiceStatus::PAID || !main_staff_plus_bursar_access())
                        ->icon('heroicon-m-document-arrow-down')
                        ->action(function ($record, $livewire) {

                            $url = URL::signedRoute('invoice.download', ['invoice' => $record->id]);

                            $livewire->js("window.location.href = '$url';");
                        }),
                ])
                    ->icon('heroicon-m-document-text')
                    ->tooltip('Export receipt'),

            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageInvoices::route('/'),
        ];
    }
}
